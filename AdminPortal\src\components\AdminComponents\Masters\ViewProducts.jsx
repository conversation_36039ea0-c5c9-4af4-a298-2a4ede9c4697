import React, { useState, useMemo } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Typography,
    Box,
    Grid,
    Card,
    CardContent,
    Chip,
    LinearProgress,
    Divider,
    IconButton,
    Tabs,
    Tab,
    Avatar,
    Tooltip,
    Badge
} from '@mui/material';
import {
    Close,
    CheckCircle,
    Warning,
    Error,
    People,
    PersonAdd,
    Security,
    Visibility,
    Edit,
    Schedule,
    TrendingUp,
    Assessment,
    Business,
    Extension
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import { theme, AdminComponents } from '../../../styles/theme';
import { suitesData } from '../../../data/suites';
import { modulesData } from '../../../data/modules';

// Mock data for customer products with license and user information
const generateMockProductData = (customerId, customerName) => {
    const getRandomDate = (start, end) => {
        return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    };

    const getRandomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;

    const mockSuites = suitesData.slice(0, 2).map((suite, index) => ({
        ...suite,
        id: `suite_${index + 1}`,
        type: 'suite',
        licenseInfo: {
            status: index === 0 ? 'Active' : 'Expiring Soon',
            startDate: getRandomDate(new Date(2023, 0, 1), new Date(2023, 6, 1)),
            endDate: getRandomDate(new Date(2024, 6, 1), new Date(2025, 0, 1)),
            nextRenewalDate: getRandomDate(new Date(2024, 10, 1), new Date(2025, 2, 1)),
            daysRemaining: getRandomInt(30, 365),
            renewalNotice: index === 1,
            completionPercentage: getRandomInt(60, 95)
        },
        userAccess: {
            totalLicensedUsers: getRandomInt(50, 200),
            activeUsers: getRandomInt(30, 150),
            inactiveUsers: getRandomInt(5, 50),
            readOnlyUsers: getRandomInt(10, 40),
            readWriteUsers: getRandomInt(20, 80),
            adminUsers: getRandomInt(2, 10)
        }
    }));

    const mockModules = modulesData.slice(0, 4).map((module, index) => ({
        ...module,
        type: 'module',
        licenseInfo: {
            status: ['Active', 'Active', 'Expired', 'Expiring Soon'][index],
            startDate: getRandomDate(new Date(2023, 0, 1), new Date(2023, 6, 1)),
            endDate: getRandomDate(new Date(2024, 6, 1), new Date(2025, 0, 1)),
            nextRenewalDate: getRandomDate(new Date(2024, 10, 1), new Date(2025, 2, 1)),
            daysRemaining: [180, 90, -30, 45][index],
            renewalNotice: index === 3,
            completionPercentage: getRandomInt(40, 90)
        },
        userAccess: {
            totalLicensedUsers: getRandomInt(20, 100),
            activeUsers: getRandomInt(15, 80),
            inactiveUsers: getRandomInt(2, 20),
            readOnlyUsers: getRandomInt(5, 25),
            readWriteUsers: getRandomInt(10, 50),
            adminUsers: getRandomInt(1, 5)
        }
    }));

    return {
        suites: mockSuites,
        modules: mockModules
    };
};

const getStatusColor = (status) => {
    switch (status) {
        case 'Active': return 'success';
        case 'Expiring Soon': return 'warning';
        case 'Expired': return 'error';
        default: return 'default';
    }
};

const getStatusIcon = (status) => {
    switch (status) {
        case 'Active': return <CheckCircle />;
        case 'Expiring Soon': return <Warning />;
        case 'Expired': return <Error />;
        default: return <CheckCircle />;
    }
};

const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const ProductCard = ({ product }) => {
    const { licenseInfo, userAccess } = product;
    const statusColor = getStatusColor(licenseInfo.status);
    const StatusIcon = getStatusIcon(licenseInfo.status);

    return (
        <AdminComponents.CardBase sx={{ mb: 3, position: 'relative' }}>
            <CardContent>
                {/* Header */}
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Box display="flex" alignItems="center" gap={2}>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                            {product.type === 'suite' ? <Business /> : <Extension />}
                        </Avatar>
                        <Box>
                            <Typography variant="h6" component="div">
                                {product.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                {product.category} • {product.type.charAt(0).toUpperCase() + product.type.slice(1)}
                            </Typography>
                        </Box>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1}>
                        <Chip
                            icon={StatusIcon}
                            label={licenseInfo.status}
                            color={statusColor}
                            size="small"
                        />
                        {licenseInfo.renewalNotice && (
                            <Tooltip title="Renewal notice sent">
                                <Badge color="warning" variant="dot">
                                    <Schedule />
                                </Badge>
                            </Tooltip>
                        )}
                    </Box>
                </Box>

                {/* License Information */}
                <AdminComponents.FormSection>
                    <AdminComponents.FormSectionTitle variant="subtitle2">
                        License & Validity Information
                    </AdminComponents.FormSectionTitle>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6} md={3}>
                            <Typography variant="caption" color="text.secondary">Start Date</Typography>
                            <Typography variant="body2">{formatDate(licenseInfo.startDate)}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <Typography variant="caption" color="text.secondary">End Date</Typography>
                            <Typography variant="body2">{formatDate(licenseInfo.endDate)}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <Typography variant="caption" color="text.secondary">Next Renewal</Typography>
                            <Typography variant="body2">{formatDate(licenseInfo.nextRenewalDate)}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <Typography variant="caption" color="text.secondary">Days Remaining</Typography>
                            <Typography 
                                variant="body2" 
                                color={licenseInfo.daysRemaining < 60 ? 'error.main' : 'text.primary'}
                            >
                                {licenseInfo.daysRemaining > 0 ? `${licenseInfo.daysRemaining} days` : 'Expired'}
                            </Typography>
                        </Grid>
                    </Grid>
                    
                    <Box mt={2}>
                        <Typography variant="caption" color="text.secondary">
                            Completion Progress ({licenseInfo.completionPercentage}%)
                        </Typography>
                        <LinearProgress 
                            variant="determinate" 
                            value={licenseInfo.completionPercentage} 
                            sx={{ mt: 1, height: 8, borderRadius: 4 }}
                        />
                    </Box>
                </AdminComponents.FormSection>

                {/* User Access Analytics */}
                <AdminComponents.FormSection>
                    <AdminComponents.FormSectionTitle variant="subtitle2">
                        User Access Analytics
                    </AdminComponents.FormSectionTitle>
                    <Grid container spacing={2}>
                        <Grid item xs={6} sm={4} md={2}>
                            <Box textAlign="center">
                                <Typography variant="h6" color="primary.main">
                                    {userAccess.totalLicensedUsers}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                    Total Licensed
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid item xs={6} sm={4} md={2}>
                            <Box textAlign="center">
                                <Typography variant="h6" color="success.main">
                                    {userAccess.activeUsers}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                    Active Users
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid item xs={6} sm={4} md={2}>
                            <Box textAlign="center">
                                <Typography variant="h6" color="text.secondary">
                                    {userAccess.inactiveUsers}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                    Inactive Users
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid item xs={6} sm={4} md={2}>
                            <Box textAlign="center">
                                <Typography variant="h6" color="info.main">
                                    {userAccess.readOnlyUsers}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                    Read-only
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid item xs={6} sm={4} md={2}>
                            <Box textAlign="center">
                                <Typography variant="h6" color="warning.main">
                                    {userAccess.readWriteUsers}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                    Read & Write
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid item xs={6} sm={4} md={2}>
                            <Box textAlign="center">
                                <Typography variant="h6" color="error.main">
                                    {userAccess.adminUsers}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                    Admin Access
                                </Typography>
                            </Box>
                        </Grid>
                    </Grid>
                </AdminComponents.FormSection>
            </CardContent>
        </AdminComponents.CardBase>
    );
};

const TabPanel = ({ children, value, index, ...other }) => (
    <div
        role="tabpanel"
        hidden={value !== index}
        id={`products-tabpanel-${index}`}
        aria-labelledby={`products-tab-${index}`}
        {...other}
    >
        {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
);

const ViewProducts = ({ open, onClose, customer }) => {
    const [tabValue, setTabValue] = useState(0);
    
    const productData = useMemo(() => {
        if (!customer) return { suites: [], modules: [] };
        return generateMockProductData(customer.id, customer.name);
    }, [customer]);

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    if (!customer) return null;

    return (
        <ThemeProvider theme={theme}>
            <Dialog
                open={open}
                onClose={onClose}
                maxWidth="lg"
                fullWidth
                PaperProps={{
                    sx: { height: '90vh' }
                }}
            >
                <DialogTitle>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Box>
                            <Typography variant="h5" component="div">
                                Products for {customer.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                License and user access information
                            </Typography>
                        </Box>
                        <IconButton onClick={onClose} size="small">
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                
                <Box sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}>
                    <Tabs value={tabValue} onChange={handleTabChange}>
                        <Tab 
                            label={`Suites (${productData.suites.length})`} 
                            icon={<Business />}
                            iconPosition="start"
                        />
                        <Tab 
                            label={`Modules (${productData.modules.length})`} 
                            icon={<Extension />}
                            iconPosition="start"
                        />
                    </Tabs>
                </Box>

                <DialogContent sx={{ height: 'calc(90vh - 200px)', overflow: 'auto' }}>
                    <TabPanel value={tabValue} index={0}>
                        {productData.suites.length > 0 ? (
                            productData.suites.map((suite) => (
                                <ProductCard key={suite.id} product={suite} />
                            ))
                        ) : (
                            <AdminComponents.CenteredMessage>
                                <Typography variant="h6" color="text.secondary">
                                    No suites found for this customer
                                </Typography>
                            </AdminComponents.CenteredMessage>
                        )}
                    </TabPanel>
                    
                    <TabPanel value={tabValue} index={1}>
                        {productData.modules.length > 0 ? (
                            productData.modules.map((module) => (
                                <ProductCard key={module.id} product={module} />
                            ))
                        ) : (
                            <AdminComponents.CenteredMessage>
                                <Typography variant="h6" color="text.secondary">
                                    No modules found for this customer
                                </Typography>
                            </AdminComponents.CenteredMessage>
                        )}
                    </TabPanel>
                </DialogContent>

                <DialogActions>
                    <Button onClick={onClose} variant="outlined">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>
        </ThemeProvider>
    );
};

export default ViewProducts;
