import React, { useState, useMemo } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Typography,
    Box,
    Grid,
    Card,
    CardContent,
    Chip,
    LinearProgress,
    Divider,
    IconButton,
    Tabs,
    Tab,
    Avatar,
    Tooltip,
    Badge,
    Paper,
    Stack,
    useTheme,
    alpha
} from '@mui/material';
import {
    Close,
    CheckCircle,
    Warning,
    Error,
    People,
    PersonAdd,
    Security,
    Visibility,
    Edit,
    Schedule,
    TrendingUp,
    Assessment,
    Business,
    Extension,
    CalendarToday,
    AccessTime,
    Group,
    AdminPanelSettings,
    VisibilityOutlined,
    EditOutlined
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import { theme, AdminComponents } from '../../../styles/theme';
import { suitesData } from '../../../data/suites';
import { modulesData } from '../../../data/modules';

// Mock data for customer products with license and user information
const generateMockProductData = (customerId, customerName) => {
    const getRandomDate = (start, end) => {
        return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    };

    const getRandomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;

    const mockSuites = suitesData.slice(0, 2).map((suite, index) => ({
        ...suite,
        id: `customer_${customerId}_suite_${index + 1}`,
        type: 'suite',
        licenseInfo: {
            status: index === 0 ? 'Active' : 'Expiring Soon',
            startDate: getRandomDate(new Date(2023, 0, 1), new Date(2023, 6, 1)),
            endDate: getRandomDate(new Date(2024, 6, 1), new Date(2025, 0, 1)),
            nextRenewalDate: getRandomDate(new Date(2024, 10, 1), new Date(2025, 2, 1)),
            daysRemaining: getRandomInt(30, 365),
            renewalNotice: index === 1,
            completionPercentage: getRandomInt(60, 95)
        },
        userAccess: {
            totalLicensedUsers: getRandomInt(50, 200),
            activeUsers: getRandomInt(30, 150),
            inactiveUsers: getRandomInt(5, 50),
            readOnlyUsers: getRandomInt(10, 40),
            readWriteUsers: getRandomInt(20, 80),
            adminUsers: getRandomInt(2, 10)
        }
    }));

    const mockModules = modulesData.slice(0, 4).map((module, index) => ({
        ...module,
        id: `customer_${customerId}_module_${index + 1}`,
        type: 'module',
        licenseInfo: {
            status: ['Active', 'Active', 'Expired', 'Expiring Soon'][index],
            startDate: getRandomDate(new Date(2023, 0, 1), new Date(2023, 6, 1)),
            endDate: getRandomDate(new Date(2024, 6, 1), new Date(2025, 0, 1)),
            nextRenewalDate: getRandomDate(new Date(2024, 10, 1), new Date(2025, 2, 1)),
            daysRemaining: [180, 90, -30, 45][index],
            renewalNotice: index === 3,
            completionPercentage: getRandomInt(40, 90)
        },
        userAccess: {
            totalLicensedUsers: getRandomInt(20, 100),
            activeUsers: getRandomInt(15, 80),
            inactiveUsers: getRandomInt(2, 20),
            readOnlyUsers: getRandomInt(5, 25),
            readWriteUsers: getRandomInt(10, 50),
            adminUsers: getRandomInt(1, 5)
        }
    }));

    return {
        suites: mockSuites,
        modules: mockModules
    };
};

const getStatusColor = (status) => {
    switch (status) {
        case 'Active': return 'success';
        case 'Expiring Soon': return 'warning';
        case 'Expired': return 'error';
        default: return 'default';
    }
};

const getStatusIcon = (status) => {
    switch (status) {
        case 'Active': return <CheckCircle />;
        case 'Expiring Soon': return <Warning />;
        case 'Expired': return <Error />;
        default: return <CheckCircle />;
    }
};

const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const ProductCard = ({ product }) => {
    const { licenseInfo, userAccess } = product;
    const statusColor = getStatusColor(licenseInfo.status);
    const StatusIcon = getStatusIcon(licenseInfo.status);
    const theme = useTheme();

    return (
        <Card
            sx={{
                mb: 3,
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                transition: 'all 0.3s ease',
                '&:hover': {
                    boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
                    transform: 'translateY(-2px)'
                }
            }}
        >
            <CardContent sx={{ p: 3 }}>
                {/* Header Section */}
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
                    <Box display="flex" alignItems="center" gap={2}>
                        <Avatar
                            sx={{
                                bgcolor: product.type === 'suite' ? 'primary.main' : 'secondary.main',
                                width: 56,
                                height: 56,
                                boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                            }}
                        >
                            {product.type === 'suite' ? <Business fontSize="large" /> : <Extension fontSize="large" />}
                        </Avatar>
                        <Box>
                            <Typography variant="h5" component="div" fontWeight="600" mb={0.5}>
                                {product.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ opacity: 0.8 }}>
                                {product.category} • {product.type.charAt(0).toUpperCase() + product.type.slice(1)}
                            </Typography>
                            {product.description && (
                                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block', maxWidth: 400 }}>
                                    {product.description}
                                </Typography>
                            )}
                        </Box>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1.5}>
                        <Chip
                            icon={<StatusIcon />}
                            label={licenseInfo.status}
                            color={statusColor}
                            variant="filled"
                            sx={{
                                fontWeight: 600,
                                px: 1,
                                '& .MuiChip-icon': { fontSize: '1rem' }
                            }}
                        />
                        {licenseInfo.renewalNotice && (
                            <Tooltip title="Renewal notice sent" arrow>
                                <Badge color="warning" variant="dot">
                                    <Schedule color="warning" />
                                </Badge>
                            </Tooltip>
                        )}
                    </Box>
                </Box>

                {/* License Information Section */}
                <Paper
                    elevation={0}
                    sx={{
                        p: 3,
                        mb: 3,
                        bgcolor: alpha(theme.palette.primary.main, 0.02),
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        borderRadius: 2
                    }}
                >
                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                        <CalendarToday color="primary" fontSize="small" />
                        <Typography variant="h6" fontWeight="600" color="primary.main">
                            License & Validity Information
                        </Typography>
                    </Box>

                    <Grid container spacing={3}>
                        <Grid key="start-date" item xs={12} sm={6} md={3}>
                            <Box>
                                <Typography variant="caption" color="text.secondary" fontWeight="500">
                                    Start Date
                                </Typography>
                                <Typography variant="body1" fontWeight="600" sx={{ mt: 0.5 }}>
                                    {formatDate(licenseInfo.startDate)}
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid key="end-date" item xs={12} sm={6} md={3}>
                            <Box>
                                <Typography variant="caption" color="text.secondary" fontWeight="500">
                                    End Date
                                </Typography>
                                <Typography variant="body1" fontWeight="600" sx={{ mt: 0.5 }}>
                                    {formatDate(licenseInfo.endDate)}
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid key="next-renewal" item xs={12} sm={6} md={3}>
                            <Box>
                                <Typography variant="caption" color="text.secondary" fontWeight="500">
                                    Next Renewal
                                </Typography>
                                <Typography variant="body1" fontWeight="600" sx={{ mt: 0.5 }}>
                                    {formatDate(licenseInfo.nextRenewalDate)}
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid key="days-remaining" item xs={12} sm={6} md={3}>
                            <Box>
                                <Typography variant="caption" color="text.secondary" fontWeight="500">
                                    Days Remaining
                                </Typography>
                                <Typography
                                    variant="body1"
                                    fontWeight="600"
                                    sx={{ mt: 0.5 }}
                                    color={licenseInfo.daysRemaining < 60 ? 'error.main' : 'success.main'}
                                >
                                    {licenseInfo.daysRemaining > 0 ? `${licenseInfo.daysRemaining} days` : 'Expired'}
                                </Typography>
                            </Box>
                        </Grid>
                    </Grid>

                    <Box mt={3}>
                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                            <Typography variant="body2" color="text.secondary" fontWeight="500">
                                Deployment Progress
                            </Typography>
                            <Typography variant="body2" fontWeight="600" color="primary.main">
                                {licenseInfo.completionPercentage}%
                            </Typography>
                        </Box>
                        <LinearProgress
                            variant="determinate"
                            value={licenseInfo.completionPercentage}
                            sx={{
                                height: 10,
                                borderRadius: 5,
                                bgcolor: alpha(theme.palette.primary.main, 0.1),
                                '& .MuiLinearProgress-bar': {
                                    borderRadius: 5,
                                    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`
                                }
                            }}
                        />
                    </Box>
                </Paper>

                {/* User Access Analytics Section */}
                <Paper
                    elevation={0}
                    sx={{
                        p: 3,
                        bgcolor: alpha(theme.palette.secondary.main, 0.02),
                        border: `1px solid ${alpha(theme.palette.secondary.main, 0.1)}`,
                        borderRadius: 2
                    }}
                >
                    <Box display="flex" alignItems="center" gap={1} mb={3}>
                        <Group color="secondary" fontSize="small" />
                        <Typography variant="h6" fontWeight="600" color="secondary.main">
                            User Access Analytics
                        </Typography>
                    </Box>

                    <Grid container spacing={3}>
                        <Grid key="total-users" item xs={6} sm={4} md={2}>
                            <Paper
                                elevation={0}
                                sx={{
                                    p: 2,
                                    textAlign: 'center',
                                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                                    borderRadius: 2,
                                    transition: 'all 0.2s ease',
                                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 }
                                }}
                            >
                                <Typography variant="h4" fontWeight="700" color="primary.main" mb={0.5}>
                                    {userAccess.totalLicensedUsers}
                                </Typography>
                                <Typography variant="caption" color="text.secondary" fontWeight="500">
                                    Total Licensed
                                </Typography>
                            </Paper>
                        </Grid>
                        <Grid key="active-users" item xs={6} sm={4} md={2}>
                            <Paper
                                elevation={0}
                                sx={{
                                    p: 2,
                                    textAlign: 'center',
                                    bgcolor: alpha(theme.palette.success.main, 0.05),
                                    border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
                                    borderRadius: 2,
                                    transition: 'all 0.2s ease',
                                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 }
                                }}
                            >
                                <Typography variant="h4" fontWeight="700" color="success.main" mb={0.5}>
                                    {userAccess.activeUsers}
                                </Typography>
                                <Typography variant="caption" color="text.secondary" fontWeight="500">
                                    Active Users
                                </Typography>
                            </Paper>
                        </Grid>
                        <Grid key="inactive-users" item xs={6} sm={4} md={2}>
                            <Paper
                                elevation={0}
                                sx={{
                                    p: 2,
                                    textAlign: 'center',
                                    bgcolor: alpha(theme.palette.grey[500], 0.05),
                                    border: `1px solid ${alpha(theme.palette.grey[500], 0.2)}`,
                                    borderRadius: 2,
                                    transition: 'all 0.2s ease',
                                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 }
                                }}
                            >
                                <Typography variant="h4" fontWeight="700" color="text.secondary" mb={0.5}>
                                    {userAccess.inactiveUsers}
                                </Typography>
                                <Typography variant="caption" color="text.secondary" fontWeight="500">
                                    Inactive Users
                                </Typography>
                            </Paper>
                        </Grid>
                        <Grid key="readonly-users" item xs={6} sm={4} md={2}>
                            <Paper
                                elevation={0}
                                sx={{
                                    p: 2,
                                    textAlign: 'center',
                                    bgcolor: alpha(theme.palette.info.main, 0.05),
                                    border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                                    borderRadius: 2,
                                    transition: 'all 0.2s ease',
                                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 }
                                }}
                            >
                                <Box display="flex" alignItems="center" justifyContent="center" gap={0.5} mb={0.5}>
                                    <VisibilityOutlined fontSize="small" color="info" />
                                    <Typography variant="h4" fontWeight="700" color="info.main">
                                        {userAccess.readOnlyUsers}
                                    </Typography>
                                </Box>
                                <Typography variant="caption" color="text.secondary" fontWeight="500">
                                    Read-only
                                </Typography>
                            </Paper>
                        </Grid>
                        <Grid key="readwrite-users" item xs={6} sm={4} md={2}>
                            <Paper
                                elevation={0}
                                sx={{
                                    p: 2,
                                    textAlign: 'center',
                                    bgcolor: alpha(theme.palette.warning.main, 0.05),
                                    border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`,
                                    borderRadius: 2,
                                    transition: 'all 0.2s ease',
                                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 }
                                }}
                            >
                                <Box display="flex" alignItems="center" justifyContent="center" gap={0.5} mb={0.5}>
                                    <EditOutlined fontSize="small" color="warning" />
                                    <Typography variant="h4" fontWeight="700" color="warning.main">
                                        {userAccess.readWriteUsers}
                                    </Typography>
                                </Box>
                                <Typography variant="caption" color="text.secondary" fontWeight="500">
                                    Read & Write
                                </Typography>
                            </Paper>
                        </Grid>
                        <Grid key="admin-users" item xs={6} sm={4} md={2}>
                            <Paper
                                elevation={0}
                                sx={{
                                    p: 2,
                                    textAlign: 'center',
                                    bgcolor: alpha(theme.palette.error.main, 0.05),
                                    border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,
                                    borderRadius: 2,
                                    transition: 'all 0.2s ease',
                                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 }
                                }}
                            >
                                <Box display="flex" alignItems="center" justifyContent="center" gap={0.5} mb={0.5}>
                                    <AdminPanelSettings fontSize="small" color="error" />
                                    <Typography variant="h4" fontWeight="700" color="error.main">
                                        {userAccess.adminUsers}
                                    </Typography>
                                </Box>
                                <Typography variant="caption" color="text.secondary" fontWeight="500">
                                    Admin Access
                                </Typography>
                            </Paper>
                        </Grid>
                    </Grid>
                </Paper>
            </CardContent>
        </Card>
    );
};

const TabPanel = ({ children, value, index, ...other }) => (
    <div
        role="tabpanel"
        hidden={value !== index}
        id={`products-tabpanel-${index}`}
        aria-labelledby={`products-tab-${index}`}
        {...other}
    >
        {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
);

const ViewProducts = ({ open, onClose, customer }) => {
    const [tabValue, setTabValue] = useState(0);
    
    const productData = useMemo(() => {
        if (!customer) return { suites: [], modules: [] };
        return generateMockProductData(customer.id, customer.name);
    }, [customer]);

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    if (!customer) return null;

    return (
        <ThemeProvider theme={theme}>
            <Dialog
                open={open}
                onClose={onClose}
                maxWidth="xl"
                fullWidth
                sx={{
                    '& .MuiDialog-paper': {
                        height: '95vh',
                        borderRadius: 3,
                        boxShadow: '0 20px 60px rgba(0,0,0,0.15)',
                    }
                }}
            >
                {/* Enhanced Header */}
                <DialogTitle
                    sx={{
                        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
                        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                        pb: 2
                    }}
                >
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Box display="flex" alignItems="center" gap={2}>
                            <Avatar
                                sx={{
                                    bgcolor: 'primary.main',
                                    width: 48,
                                    height: 48,
                                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                                }}
                            >
                                <Assessment />
                            </Avatar>
                            <Box>
                                <Typography variant="h4" component="div" fontWeight="700" mb={0.5}>
                                    Products Portfolio
                                </Typography>
                                <Typography variant="h6" color="text.secondary" fontWeight="500">
                                    {customer.name} • License & User Analytics
                                </Typography>
                            </Box>
                        </Box>
                        <IconButton
                            onClick={onClose}
                            sx={{
                                bgcolor: alpha(theme.palette.grey[500], 0.1),
                                '&:hover': { bgcolor: alpha(theme.palette.grey[500], 0.2) }
                            }}
                        >
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>

                {/* Enhanced Tabs */}
                <Box
                    sx={{
                        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                        px: 3,
                        bgcolor: alpha(theme.palette.background.paper, 0.8)
                    }}
                >
                    <Tabs
                        value={tabValue}
                        onChange={handleTabChange}
                        sx={{
                            '& .MuiTab-root': {
                                minHeight: 64,
                                fontWeight: 600,
                                fontSize: '1rem',
                                textTransform: 'none',
                                '&.Mui-selected': {
                                    color: 'primary.main'
                                }
                            },
                            '& .MuiTabs-indicator': {
                                height: 3,
                                borderRadius: 1.5
                            }
                        }}
                    >
                        <Tab
                            label={
                                <Box display="flex" alignItems="center" gap={1}>
                                    <Business />
                                    <Box>
                                        <Typography variant="body1" fontWeight="600">
                                            Suites
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary">
                                            {productData.suites.length} products
                                        </Typography>
                                    </Box>
                                </Box>
                            }
                        />
                        <Tab
                            label={
                                <Box display="flex" alignItems="center" gap={1}>
                                    <Extension />
                                    <Box>
                                        <Typography variant="body1" fontWeight="600">
                                            Modules
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary">
                                            {productData.modules.length} products
                                        </Typography>
                                    </Box>
                                </Box>
                            }
                        />
                    </Tabs>
                </Box>

                {/* Enhanced Content */}
                <DialogContent
                    sx={{
                        height: 'calc(95vh - 220px)',
                        overflow: 'auto',
                        p: 0,
                        bgcolor: alpha(theme.palette.grey[50], 0.3)
                    }}
                >
                    <Box sx={{ p: 3 }}>
                        <TabPanel value={tabValue} index={0}>
                            {productData.suites.length > 0 ? (
                                <Stack spacing={3}>
                                    {productData.suites.map((suite) => (
                                        <ProductCard key={suite.id} product={suite} />
                                    ))}
                                </Stack>
                            ) : (
                                <Paper
                                    sx={{
                                        p: 6,
                                        textAlign: 'center',
                                        bgcolor: 'background.paper',
                                        borderRadius: 3,
                                        border: `2px dashed ${alpha(theme.palette.divider, 0.3)}`
                                    }}
                                >
                                    <Business sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
                                    <Typography variant="h5" color="text.secondary" fontWeight="600" mb={1}>
                                        No Suites Found
                                    </Typography>
                                    <Typography variant="body1" color="text.secondary">
                                        This customer doesn't have any suite products yet.
                                    </Typography>
                                </Paper>
                            )}
                        </TabPanel>

                        <TabPanel value={tabValue} index={1}>
                            {productData.modules.length > 0 ? (
                                <Stack spacing={3}>
                                    {productData.modules.map((module) => (
                                        <ProductCard key={module.id} product={module} />
                                    ))}
                                </Stack>
                            ) : (
                                <Paper
                                    sx={{
                                        p: 6,
                                        textAlign: 'center',
                                        bgcolor: 'background.paper',
                                        borderRadius: 3,
                                        border: `2px dashed ${alpha(theme.palette.divider, 0.3)}`
                                    }}
                                >
                                    <Extension sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
                                    <Typography variant="h5" color="text.secondary" fontWeight="600" mb={1}>
                                        No Modules Found
                                    </Typography>
                                    <Typography variant="body1" color="text.secondary">
                                        This customer doesn't have any module products yet.
                                    </Typography>
                                </Paper>
                            )}
                        </TabPanel>
                    </Box>
                </DialogContent>

                {/* Enhanced Footer */}
                <DialogActions
                    sx={{
                        p: 3,
                        borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                        bgcolor: alpha(theme.palette.background.paper, 0.8)
                    }}
                >
                    <Button
                        onClick={onClose}
                        variant="contained"
                        size="large"
                        sx={{
                            minWidth: 120,
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 600
                        }}
                    >
                        Close
                    </Button>
                </DialogActions>
            </Dialog>
        </ThemeProvider>
    );
};

export default ViewProducts;
