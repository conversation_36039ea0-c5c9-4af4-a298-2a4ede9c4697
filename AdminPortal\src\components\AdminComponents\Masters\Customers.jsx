import React, { useState, useMemo, useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    Drawer, ToggleButton, Avatar, List, ListItem, ListItemText, FormControlLabel, Menu, MenuItem,
    Select, InputLabel, FormControl, Chip, Card, ListItemIcon, ToggleButtonGroup,
    TableContainer, CardContent, Divider, Dialog, DialogContent, DialogActions, Grid,
    Tabs, Tab, Accordion, AccordionSummary, AccordionDetails, LinearProgress,
    Stepper, Step, StepLabel, DialogTitle
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, <PERSON><PERSON>, <PERSON><PERSON>ist,
    <PERSON><PERSON>hart, Schedule, People, CheckCircle, Cancel, MoreVert, Close, <PERSON><PERSON>ff,
    <PERSON><PERSON>hart, <PERSON><PERSON>hart, DonutLarge, Settings, Save, Home, NavigateNext, ExpandMore, Check, NavigateBefore
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import ViewProducts from './ViewProducts';


// --- MOCK DATA & CONFIGS ---
const initialCustomers = [
    // { id: 1, name: 'Innovate Corp', shortName: 'Innovate', parentCompany: 'Synergy Ltd', industry: 'Technology', logoName: 'innovate_logo.png', address: '123 Tech Lane, Silicon Valley, CA', companyType: 'Manufacturer', currency: 'USD', remarks: 'Key technology partner.', defaultGridSize: 10, isActive: true, companyTheme: 'Default', companyFont: 'Roboto', inventoryFactor: '5', workOrderCushion: '24', orderingCost: '50', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'John Doe', email: '<EMAIL>', phone: '************' }, isDraft: false },
    // { id: 2, name: 'Synergy Ltd', shortName: 'Synergy', parentCompany: 'Innovate Corp', industry: 'Healthcare', logoName: 'synergy_logo.png', address: '456 Health Blvd, Boston, MA', companyType: 'Manufacturer', currency: 'USD', remarks: 'Growing healthcare provider.', defaultGridSize: 12, isActive: true, companyTheme: 'Mint', companyFont: 'Arial', inventoryFactor: '7', workOrderCushion: '48', orderingCost: '35', status: 'Active', type: 'Mid-Market', customerType: 'EN', primaryContact: { name: 'Jane Smith', email: '<EMAIL>', phone: '************' }, isDraft: false },
    // { id: 3, name: 'Quantum Solutions', shortName: 'Quantum', parentCompany: '', industry: 'Finance', logoName: 'quantum_logo.png', address: '789 Finance Ave, New York, NY', companyType: 'Partner', currency: 'USD', remarks: 'Financial consulting firm.', defaultGridSize: 8, isActive: false, companyTheme: 'Default', companyFont: 'Times New Roman', inventoryFactor: '10', workOrderCushion: '12', orderingCost: '100', status: 'Inactive', type: 'SMB', customerType: 'NN', primaryContact: { name: 'Peter Jones', email: '<EMAIL>', phone: '************' }, isDraft: false },
    { id: 4, name: 'Volvo', industry: 'Automotive', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Anna Svensson', email: '<EMAIL>', phone: '+46-31-66-90-00' }, isDraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRx9JJ9OA98rh2dsYOP_FRGJyVJT66_9QQQUg&s' },
    { id: 5, name: 'Daimler', industry: 'Automotive', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Klaus Fischer', email: '<EMAIL>', phone: '+49-711-17-0' }, isDraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT8oM91HS9ujJzEL3ZVBmqoDFmBNYZgJVqgNQ&s' },
    { id: 6, name: 'Komatsu', industry: 'Construction Equipment', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Kenji Takahashi', email: '<EMAIL>', phone: '+81-3-5561-2616' }, isDraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQbBXoD97gF__uJmBlPYFVTOLZD-2YisSPOLQ&s' },
    { id: 7, name: 'MAN', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Stefan Schmidt', email: '<EMAIL>', phone: '+49-89-1580-0' }, isDraft: false, logoName: 'https://cdn.worldvectorlogo.com/logos/man-logo.svg' },
    { id: 8, name: 'Tata Motors', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Ravi Kumar', email: '<EMAIL>', phone: '+91-22-6665-8282' }, isDraft: false, logoName: 'https://cdn.worldvectorlogo.com/logos/tata-motors.svg' },
    { id: 9, name: 'Eicher', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Ankit Sharma', email: '<EMAIL>', phone: '+91-************' }, isDraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTwOH_SJGcRbwzS3DMdgEZj6sQ_NMnyfOgEYw&s' },
    { id: 10, name: 'Sandvik', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Lars Berg', email: '<EMAIL>', phone: '+46-8-456-11-00' }, isDraft: false, logoName: 'https://cdn.worldvectorlogo.com/logos/sandvik-1.svg' },
    { id: 11, name: 'Claas', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Jürgen Klein', email: '<EMAIL>', phone: '+49-5247-12-0' }, isDraft: false, logoName: 'https://cdn.worldvectorlogo.com/logos/claas.svg' },
    { id: 12, name: 'Yanmar', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Yuki Tanaka', email: '<EMAIL>', phone: '+81-6-6376-6211' }, isDraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQa32ERni2J5NQAlvaufRuqvS1eqeL9A9yxEg&s' },
    { id: 13, name: 'Wirtgen Group', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Hans Weber', email: '<EMAIL>', phone: '+49-2645-131-0' }, isDraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQNhzSMuMS9Nv2Hpm5iLCxyC1eIDYXyKQfLxg&s' },
    { id: 14, name: 'Prevost', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Marc Tremblay', email: '<EMAIL>', phone: '******-883-3391' }, isDraft: false, logoName: 'https://logos-world.net/wp-content/uploads/2023/09/Prevost-Logo.jpg' },
    { id: 15, name: 'Kohler', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'David Lee', email: '<EMAIL>', phone: '******-456-4537' }, isDraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSsc5WJrSdCW2VKEKPluLH8ZFElZCQ_KXoQUw&s' },
    { id: 16, name: 'Homag', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Andreas Meyer', email: '<EMAIL>', phone: '+49-7443-13-0' }, isDraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS0NZF4UlaLK4P8uaTBUcy9K20OtCszbJ1JHw&s' },
    { id: 17, name: 'EMAG', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Matthias Koch', email: '<EMAIL>', phone: '+49-7161-209-0' }, isДraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTkdda4o0Hrx1S0fWfS_KtBI4ltcS89z0-dvQ&s' },
    { id: 18, name: 'Nova Bus', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Sophie Martin', email: '<EMAIL>', phone: '******-472-6410' }, isDraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQ2ExoDrIz7lehGtqqjSTTRAFUseXNwRQwWUQ&s' },
    { id: 19, name: 'Normet', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Mika Virtanen', email: '<EMAIL>', phone: '+358-17-83-241' }, isDraft: false, logoName: 'https://www.finncham.org.br/portal/wp-content/uploads/2020/08/Normet.png' },
    { id: 20, name: 'Meritor', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Chris Williams', email: '<EMAIL>', phone: '******-435-1000' }, isDraft: false, logoName: 'https://cdn.worldvectorlogo.com/logos/meritor-1.svg' },
    { id: 21, name: 'Lohia', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Rajesh Gupta', email: '<EMAIL>', phone: '+91-************' }, isDraft: false, logoName: 'https://plastindia-website-assets.s3.ap-south-1.amazonaws.com/1_fd957b195a.svg' },
    { id: 22, name: 'Yaskawa', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Hiroshi Sato', email: '<EMAIL>', phone: '+81-93-645-8801' }, isDraft: false, logoName: 'https://cdn.worldvectorlogo.com/logos/yaskawa-electric-corporation.svg' },
    { id: 23, name: 'Keestrack', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Jan Peeters', email: '<EMAIL>', phone: '+32-89-51-58-51' }, isDraft: false, logoName: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSQpwr_Lc0GjAiNYXVeIFTAoe3JrA5ToXJb7w&s' },
    { id: 24, name: 'SKF', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Ingrid Johansson', email: '<EMAIL>', phone: '+46-31-337-10-00' }, isDraft: false, logoName: 'https://cdn.worldvectorlogo.com/logos/skf-1.svg' },
    { id: 25, name: 'Metso', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'Antti Nieminen', email: '<EMAIL>', phone: '+358-20-484-100' }, isDraft: false, logoName: 'https://cdn.worldvectorlogo.com/logos/metso.svg' },
];

const ALL_COLUMNS = [
    { key: 'name', label: 'Name', type: 'string', groupable: true },
    { key: 'status', label: 'Status', type: 'string', groupable: true },
    { key: 'type', label: 'Tier', type: 'string', groupable: true },
    { key: 'industry', label: 'Industry', type: 'string', groupable: true },
    { key: 'customerType', label: 'Customer Type', type: 'string', groupable: true },
    { key: 'primaryContact.name', label: 'Contact Name', type: 'string', groupable: false },
    { key: 'primaryContact.email', label: 'Contact Email', type: 'string', groupable: false },
];

const CUSTOMER_TYPE_LABELS = { EE: 'Existing Existing', EN: 'Existing New', NN: 'Net New' };
const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

// --- MOCK DATA FOR DIALOG TABS ---
const mockBranches = [
    { id: 1, name: 'Branch-Hyderabad', phone: '************', location: 'Levis', email: '<EMAIL>', isHeadOffice: 'Yes', isExternal: 'No', status: 'Active' },
    { id: 2, name: 'Branch-Bangalore', phone: '************', location: 'New Jersey', email: '<EMAIL>', isHeadOffice: 'No', isExternal: 'No', status: 'Inactive' },
];
const mockRelations = [{ id: 1, name: 'TEST456', partnerType: 'Vendor', status: 'Active' }];
const mockBrands = [{ id: 1, name: 'DuraFleet', status: 'Active' }, { id: 2, name: 'Kia Motors', status: 'Active' }];
const mockEmployees = [
    { id: 1, name: 'Admin', code: 'E001', mobile: '************', email: '<EMAIL>', department: 'Administration', designation: 'Credit Agent', status: 'Active' },
    { id: 2, name: 'Employee 11', code: 'A029346', mobile: '************', email: '<EMAIL>', department: 'Service', designation: 'DMS Administrator', status: 'Active' },
];

// --- JSON DATA FOR DIALOG DROPDOWNS ---
const dialogDropdownData = {
    parentCompanies: [
        { value: 'Innovate Corp', label: 'Innovate Corp' },
        { value: 'Synergy Ltd', label: 'Synergy Ltd' },
    ],
    companyTypes: [
        { value: 'Partner', label: 'Partner' },
        { value: 'Manufacturer', label: 'Manufacturer' },
    ],
    industries: [
        { value: 'Technology', label: 'Technology' },
        { value: 'Healthcare', label: 'Healthcare' },
        { value: 'Finance', label: 'Finance' },
    ],
    currencies: [
        { value: 'USD', label: 'USD' },
        { value: 'EUR', label: 'EUR' },
        { value: 'GBP', label: 'GBP' },
    ],
    companyThemes: [
        { value: 'Default', label: 'Default' },
        { value: 'Mint', label: 'Mint' },
        { value: 'Industrial', label: 'Industrial' },
    ],
    companyFonts: [
        { value: 'Roboto', label: 'Roboto' },
        { value: 'Arial', label: 'Arial' },
        { value: 'Verdana', label: 'Verdana' },
    ],
};

const brandNameOptions = [
    "Brand 6", "Doosan Infracore", "DuraFleet", "JCB Excavators", 
    "Kia Motors", "Komatsu Ltd.", "Mahindra Tractors", "Tata Motors", 
    "Volvo Buses", "Volvo CE"
];

const partnerCompanyOptions = ["HCLTech", "HCLSoftware", "Synergy Ltd", "Innovate Corp", "Quantum Solutions"];
const partnerTypeOptions = ["Vendor", "Supplier", "Partner", "Client", "Distributor"];


// --- COLUMN DEFINITIONS FOR DIALOG TABS ---
const branchColumns = [{ key: 'name', label: 'Name' }, { key: 'location', label: 'Location' }, { key: 'phone', label: 'Phone' }, { key: 'email', label: 'Email' }, { key: 'isHeadOffice', 'label': 'Head Office?' }];
const relationColumns = [{ key: 'name', label: 'Partner/Company' }, { key: 'partnerType', label: 'Partner Type' }];
const brandColumns = [{ key: 'name', label: 'Name' }];
const employeeColumns = [{ key: 'name', label: 'Name' }, { key: 'designation', label: 'Designation' }, { key: 'department', label: 'Department' }, { key: 'email', label: 'Email' }, { key: 'mobile', label: 'Mobile' }];


// --- GENERIC COMPONENTS FOR DIALOG TAB VIEWS ---
const DialogActionButtons = () => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

const GenericItemCard = ({ item, columns, isSelected, onSelect }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox className="card-checkbox" checked={isSelected} onChange={() => onSelect(item.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer><DialogActionButtons /></AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{item[columns[0]?.key] || 'N/A'}</Typography>
            {columns[1] && <Typography color="text.secondary" noWrap gutterBottom>{item[columns[1]?.key]}</Typography>}
            {item.status && <AdminComponents.StatusBadge ownerState={{ status: item.status }} label={item.status} size="small" />}
            <AdminComponents.CardDivider />
            <AdminComponents.CardDetailsGrid>
                {columns.slice(2, 5).map(col => (
                    <React.Fragment key={col.key}>
                        <AdminComponents.CardDetailLabel variant="body2">{col.label}:</AdminComponents.CardDetailLabel>
                        <Typography variant="body2" noWrap>{item[col.key] || 'N/A'}</Typography>
                    </React.Fragment>
                ))}
            </AdminComponents.CardDetailsGrid>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const GenericCompactCard = ({ item, columns, isSelected, onSelect }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox className="card-checkbox" checked={isSelected} onChange={() => onSelect(item.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer><DialogActionButtons /></AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{item[columns[0]?.key] || 'N/A'}</Typography>
                {columns[1] && <Typography variant="caption" color="text.secondary">{item[columns[1]?.key]}</Typography>}
            </div>
            <AdminComponents.CompactCardFooter>
                {columns[2] && <Typography variant="body2" fontWeight="500">{item[columns[2]?.key]}</Typography>}
                {item.status && <AdminComponents.StatusBadge ownerState={{ status: item.status }} label={item.status} size="small" />}
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const GenericListItem = ({ item, columns, isSelected, onSelect }) => (
    <AdminComponents.ListItemCard isSelected={isSelected}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isSelected} onChange={() => onSelect(item.id)} onClick={e => e.stopPropagation()} />
            <Box>
                <Typography fontWeight="bold">{item[columns[0]?.key] || 'N/A'}</Typography>
                {columns[1] && <Typography variant="body2" color="text.secondary">{item[columns[1]?.key]}</Typography>}
            </Box>
            {columns[2] && <Typography variant="body2">{item[columns[2]?.key]}</Typography>}
            {columns[3] && <Typography variant="body2">{item[columns[3]?.key]}</Typography>}
            {item.status ? <AdminComponents.StatusBadge ownerState={{ status: item.status }} label={item.status} size="small" /> : <span />}
            <AdminComponents.ListItemActions>
                <DialogActionButtons />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);


// --- REUSABLE VIEW COMPONENT FOR DIALOG TABS ---
const DialogTabView = ({ data, columns, entityName, onAdd }) => {
    const [viewMode, setViewMode] = useState('grid');
    const [selectedIds, setSelectedIds] = useState([]);

    const handleSelectAll = (event) => {
        if (event.target.checked) {
            setSelectedIds(data.map((item) => item.id));
        } else {
            setSelectedIds([]);
        }
    };

    const handleSelectOne = (id) => {
        const selectedIndex = selectedIds.indexOf(id);
        let newSelectedIds = [];
        if (selectedIndex === -1) {
            newSelectedIds = newSelectedIds.concat(selectedIds, id);
        } else {
            newSelectedIds = selectedIds.filter(selectedId => selectedId !== id);
        }
        setSelectedIds(newSelectedIds);
    };

    const renderContent = () => {
        const isItemSelected = (id) => selectedIds.indexOf(id) !== -1;

        switch (viewMode) {
            case 'cards':
                return (
                    <AdminComponents.GridView>
                        {data.map(item => <GenericItemCard key={item.id} item={item} columns={columns} isSelected={isItemSelected(item.id)} onSelect={handleSelectOne} />)}
                    </AdminComponents.GridView>
                );
            case 'compact':
                return (
                    <AdminComponents.CompactView>
                        {data.map(item => <GenericCompactCard key={item.id} item={item} columns={columns} isSelected={isItemSelected(item.id)} onSelect={handleSelectOne} />)}
                    </AdminComponents.CompactView>
                );
            case 'list':
                return (
                    <AdminComponents.ListView>
                        {data.map(item => <GenericListItem key={item.id} item={item} columns={columns} isSelected={isItemSelected(item.id)} onSelect={handleSelectOne} />)}
                    </AdminComponents.ListView>
                );
            case 'grid':
            default:
                return (
                    <TableContainer component={Paper}>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < data.length} checked={data.length > 0 && selectedIds.length === data.length} onChange={handleSelectAll} /></TableCell>
                                    {columns.map(col => <TableCell key={col.key}>{col.label}</TableCell>)}
                                    <TableCell align="center">Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {data.map(item => {
                                    const isSelected = isItemSelected(item.id);
                                    return (
                                        <TableRow key={item.id} hover role="checkbox" aria-checked={isSelected} tabIndex={-1} selected={isSelected}>
                                            <TableCell padding="checkbox"><Checkbox checked={isSelected} onChange={() => handleSelectOne(item.id)} /></TableCell>
                                            {columns.map(col => <TableCell key={col.key}>{item[col.key] || 'N/A'}</TableCell>)}
                                            <TableCell align="center"><DialogActionButtons /></TableCell>
                                        </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>
                );
        }
    };

    return (
        <Box>
            <AdminComponents.DialogTabViewControls>
                <AdminComponents.StyledToggleButtonGroup
                    size="small"
                    value={viewMode}
                    exclusive
                    onChange={(e, v) => v && setViewMode(v)}
                >
                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                </AdminComponents.StyledToggleButtonGroup>
                <AdminComponents.DialogTabViewActions>
                    <Button variant="contained" startIcon={<Add />} onClick={onAdd}>Add {entityName}</Button>
                    {selectedIds.length > 0 && <AdminComponents.DialogTabViewDeleteButton variant="outlined" color="error" startIcon={<Delete />}>Delete ({selectedIds.length})</AdminComponents.DialogTabViewDeleteButton>}
                </AdminComponents.DialogTabViewActions>
            </AdminComponents.DialogTabViewControls>
            {renderContent()}
        </Box>
    );
};


// --- UI COMPONENTS ---
const TabPanel = (props) => {
    const { children, value, index, ...other } = props;
    return (
        <div role="tabpanel" hidden={value !== index} id={`tabpanel-${index}`} aria-labelledby={`tab-${index}`} {...other}>
            {value === index && <AdminComponents.TabPanelContainer>{children}</AdminComponents.TabPanelContainer>}
        </div>
    );
};

const CUSTOMER_STEPS = [
    'Customer',
    'Branch',
    'Company-Company Relation',
    'Brands Association',
    'Employee',
];

const BRANCH_STEPS = ['Branch Details', 'Tax Structure', 'Branch Tax Codes'];
const EMPLOYEE_STEPS = ['Employee Details', 'Skills', 'Employee - Branch'];
// Custom Step Icon Component for the Stepper
function StepIcon(props) {
    const { active, completed, className, icon } = props;
    return (
        <AdminComponents.StepIconRoot ownerState={{ active, completed }} className={className}>
            {completed ? <Check sx={{ color: 'var(--success-main)', fontSize: '1.2rem' }} /> : <AdminComponents.StepIconText>{icon}</AdminComponents.StepIconText>}
        </AdminComponents.StepIconRoot>
    );
}

const ConfirmationDialog = ({ open, onClose, onConfirm, title, children }) => {
    return (
        <Dialog
            open={open}
            onClose={onClose}
            aria-labelledby="confirmation-dialog-title"
            maxWidth="xs"
            fullWidth
        >
            <AdminComponents.DialogHeader>
                <Typography variant="h6" id="confirmation-dialog-title">{title}</Typography>
            </AdminComponents.DialogHeader>
            <AdminComponents.DialogContent sx={{ p: 4 }}>
                <Typography>{children}</Typography>
            </AdminComponents.DialogContent>
            <AdminComponents.DialogActions>
                <Button onClick={onClose}>Cancel</Button>
                <Button onClick={onConfirm} color="error" variant="contained">
                    Delete
                </Button>
            </AdminComponents.DialogActions>
        </Dialog>
    );
};


const DraftsDialog = ({ open, onClose, customers, onSelectDraft, onRequestDelete }) => {
    const drafts = customers.filter(c => c.isDraft);

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="xs" PaperProps={{ sx: { width: '360px' } }}>
            <AdminComponents.DialogHeader>
                <Typography variant="h6">Drafts</Typography>
                <IconButton onClick={onClose}><Close sx={{ color: 'var(--primary-white)' }} /></IconButton>
            </AdminComponents.DialogHeader>
            <AdminComponents.DialogContent sx={{ p: 2 }}>
                <List>
                    {drafts.length > 0 ? (
                        drafts.map(draft => (
                            <ListItem
                                button
                                key={draft.id}
                                onClick={() => onSelectDraft(draft)}
                                secondaryAction={
                                    <IconButton
                                        edge="end"
                                        aria-label="delete"
                                        title="Delete Draft"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onRequestDelete(draft.id);
                                        }}
                                    >
                                        <Delete />
                                    </IconButton>
                                }
                            >
                                <ListItemText
                                    primary={draft.name || "Untitled Draft"}
                                    secondary={`Industry: ${draft.industry || 'N/A'}`}
                                />
                            </ListItem>
                        ))
                    ) : (
                        <ListItem>
                            <ListItemText primary="No drafts found." />
                        </ListItem>
                    )}
                </List>
            </AdminComponents.DialogContent>
            <AdminComponents.DialogActions>
                <Button onClick={onClose}>Close</Button>
            </AdminComponents.DialogActions>
        </Dialog>
    );
};

// --- MOCK DATA FOR BRANCH DIALOG ---
const branchDropdownData = {
    countries: [{ value: 'India', label: 'India' }],
    states: [{ value: 'Tamil Nadu', label: 'Tamil Nadu' }],
    currencies: [{ value: 'USD', label: 'US Dollars' }, { value: 'INR', label: 'Indian Rupee' }],
    timeZones: [{ value: 'IST', label: 'India Standard Time' }],
    payrollFileTypes: [{ value: 'SAP', label: 'SAP' }, { value: 'Other', label: 'Other' }],
};
const mockTaxStructuresData = [{ id: 1, taxCodeName: 'VAT', taxCode: '15%' }];
const taxStructureColumns = [{ key: 'taxCodeName', label: 'Tax Code Name' }, { key: 'taxCode', label: 'Tax Code' }];
const mockBranchTaxCodesData = [{ id: 1, taxCodeName: 'GST', taxCode: '18%' }];
const branchTaxCodesColumns = [{ key: 'taxCodeName', label: 'Tax Code Name' }, { key: 'taxCode', label: 'Tax Code' }];

const BranchDialog = ({ open, onClose, onSave }) => {
    const [formData, setFormData] = useState({});
    const [tabValue, setTabValue] = useState(0);

    useEffect(() => {
        if (open) {
            // Reset form with empty strings and default selections
            setTabValue(0);
            setFormData({
                branchName: '',
                partnerCode: '',
                country: 'India',
                state: 'Tamil Nadu',
                region: '',
                currency: 'USD',
                payrollFileType: 'SAP',
                timeZone: 'IST',
                address: '',
                location: '',
                zipCode: '',
                phone: '',
                mobile: '',
                fax: '',
                email: '',
                isExternal: false,
                isActive: true,
                isHeadOffice: false,
            });
        }
    }, [open]);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
    };

    const handleSave = () => {
        onSave(formData);
        onClose();
    };

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const handleNextTab = () => {
        setTabValue(prev => Math.min(prev + 1, BRANCH_STEPS.length - 1));
    };

    const handlePreviousTab = () => {
        setTabValue(prev => Math.max(prev - 1, 0));
    };

    // Placeholder handlers for nested "Add" buttons
    const handleAddTaxStructure = () => {
        console.log("This should open a dialog to add a new tax structure.");
    };

    const handleAddBranchTaxCode = () => {
        console.log("This should open a dialog to add a new branch tax code.");
    };

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
            <AdminComponents.DialogHeaderContainer>
                <AdminComponents.DialogHeader>
                    <Typography variant="h6">Add Branch</Typography>
                    <Box>
                        <Button color="inherit" onClick={handleSave} startIcon={<Save />}>Save</Button>
                        <Button color="inherit" onClick={onClose} startIcon={<Close />}>Exit</Button>
                    </Box>
                </AdminComponents.DialogHeader>
                <AdminComponents.StepperBox>
                    <Stepper alternativeLabel activeStep={tabValue} connector={<AdminComponents.CustomStepConnector />}>
                        {BRANCH_STEPS.map((label, index) => (
                            <Step key={label} completed={tabValue > index}>
                                <StepLabel StepIconComponent={StepIcon}>{""}</StepLabel>
                            </Step>
                        ))}
                    </Stepper>
                </AdminComponents.StepperBox>
                <AdminComponents.TabsContainer>
                    <AdminComponents.StyledTabs value={tabValue} onChange={handleTabChange} variant="fullWidth">
                        {BRANCH_STEPS.map((label) => <AdminComponents.StyledTab key={label} label={label} />)}
                    </AdminComponents.StyledTabs>
                </AdminComponents.TabsContainer>
            </AdminComponents.DialogHeaderContainer>

            <AdminComponents.ScrollableDialogContent>
                <TabPanel value={tabValue} index={0}>
                    <AdminComponents.FormSection>
                        <AdminComponents.FormSectionTitle>Primary Information</AdminComponents.FormSectionTitle>
                        <Grid container spacing={3}>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Branch Name *" name="branchName" value={formData.branchName} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Partner Code" name="partnerCode" value={formData.partnerCode} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth variant="outlined"><InputLabel>Country *</InputLabel><Select native name="country" value={formData.country} onChange={handleChange} label="Country *">{branchDropdownData.countries.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}</Select></FormControl></Grid>
                            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth variant="outlined"><InputLabel>State *</InputLabel><Select native name="state" value={formData.state} onChange={handleChange} label="State *">{branchDropdownData.states.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}</Select></FormControl></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Region" name="region" value={formData.region} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth variant="outlined"><InputLabel>Currency</InputLabel><Select native name="currency" value={formData.currency} onChange={handleChange} label="Currency">{branchDropdownData.currencies.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}</Select></FormControl></Grid>
                            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth variant="outlined"><InputLabel>Payroll File Type</InputLabel><Select native name="payrollFileType" value={formData.payrollFileType} onChange={handleChange} label="Payroll File Type">{branchDropdownData.payrollFileTypes.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}</Select></FormControl></Grid>
                            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth variant="outlined"><InputLabel>Time Zone *</InputLabel><Select native name="timeZone" value={formData.timeZone} onChange={handleChange} label="Time Zone *">{branchDropdownData.timeZones.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}</Select></FormControl></Grid>
                        </Grid>
                    </AdminComponents.FormSection>

                    <AdminComponents.FormSection>
                        <AdminComponents.FormSectionTitle>Contact Information</AdminComponents.FormSectionTitle>
                        <Grid container spacing={3}>
                            <Grid item xs={12}><TextField label="Address *" name="address" value={formData.address} onChange={handleChange} fullWidth multiline rows={2} variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Location" name="location" value={formData.location} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Zip Code" name="zipCode" value={formData.zipCode} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Phone" name="phone" value={formData.phone} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Mobile *" name="mobile" value={formData.mobile} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Fax" name="fax" value={formData.fax} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Email *" name="email" value={formData.email} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                        </Grid>
                    </AdminComponents.FormSection>

                    <AdminComponents.FormSection>
                        <AdminComponents.FormSectionTitle>Settings</AdminComponents.FormSectionTitle>
                        <Grid container spacing={3}>
                             <Grid item xs={12} sm={4} md={2}><FormControlLabel control={<Checkbox name="isExternal" checked={formData.isExternal} onChange={handleChange} />} label="Is External?" /></Grid>
                             <Grid item xs={12} sm={4} md={2}><FormControlLabel control={<Checkbox name="isActive" checked={formData.isActive} onChange={handleChange} />} label="Is Active?" /></Grid>
                             <Grid item xs={12} sm={4} md={2}><FormControlLabel control={<Checkbox name="isHeadOffice" checked={formData.isHeadOffice} onChange={handleChange} />} label="Is Head Office?" /></Grid>
                        </Grid>
                    </AdminComponents.FormSection>

                </TabPanel>
                <TabPanel value={tabValue} index={1}>
                    <DialogTabView 
                        data={mockTaxStructuresData} 
                        columns={taxStructureColumns} 
                        entityName="Tax Structure"
                        onAdd={handleAddTaxStructure} 
                    />
                </TabPanel>
                <TabPanel value={tabValue} index={2}>
                    <DialogTabView 
                        data={mockBranchTaxCodesData} 
                        columns={branchTaxCodesColumns} 
                        entityName="Branch Tax Code"
                        onAdd={handleAddBranchTaxCode}
                    />
                </TabPanel>
            </AdminComponents.ScrollableDialogContent>
            <AdminComponents.DialogFooter>
                <AdminComponents.FooterButtonContainer>
                    <Button variant="outlined" onClick={handlePreviousTab} disabled={tabValue === 0} startIcon={<NavigateBefore />}>Previous</Button>
                    <AdminComponents.SpacedButton variant="outlined" onClick={handleNextTab} disabled={tabValue === BRANCH_STEPS.length - 1} endIcon={<NavigateNext />}>Next</AdminComponents.SpacedButton>
                </AdminComponents.FooterButtonContainer>
                <AdminComponents.FooterSpacer />
                <AdminComponents.FooterButtonContainer>
                    <Button variant="contained" startIcon={<Save />} onClick={handleSave}>Save & Close</Button>
                    <AdminComponents.SpacedButton variant="outlined" startIcon={<Close />} onClick={onClose}>Cancel</AdminComponents.SpacedButton>
                </AdminComponents.FooterButtonContainer>
            </AdminComponents.DialogFooter>
        </Dialog>
    );
};

// --- MOCK DATA FOR EMPLOYEE DIALOG ---
const employeeDropdownData = {
    departments: [{ value: 'Administration', label: 'Administration' }, { value: 'Service', label: 'Service' }],
    designations: [{ value: 'Credit Agent', label: 'Credit Agent' }, { value: 'DMS Administrator', label: 'DMS Administrator' }],
    countries: [{ value: 'Canada', label: 'Canada' }, { value: 'USA', label: 'USA' }],
    states: [{ value: 'Quebec', label: 'Quebec' }, { value: 'Ontario', label: 'Ontario' }],
    managers: [{ value: 'None', label: '--Select--' }],
};
const mockSkillsData = [{ id: 1, skillSet: 'Electrical', rating: 4 }];
const skillsColumns = [
    { key: 'skillSet', label: 'Skill Set' },
    { key: 'rating', label: 'Rating' }
];
const mockEmployeeBranchesData = [
    { id: 1, name: 'Branch1-Hyderabad', status: 'Active' }, { id: 2, name: 'Branch5-Bangalore', status: 'Active' }, { id: 3, name: 'New Holland - Bhopal', status: 'Inactive' },
    { id: 4, name: 'Branch2-Lucknow', status: 'Active' }, { id: 5, name: 'Eicher - Nagpur', status: 'Active' }, { id: 6, name: 'Mahindra - Chennai', status: 'Active' },
];
const employeeBranchesColumns = [{ key: 'name', label: 'Branch' }, {key: 'status', label: 'Status'}];

const EmployeeDialog = ({ open, onClose, onSave }) => {
    const [formData, setFormData] = useState({});
    const [tabValue, setTabValue] = useState(0);

    useEffect(() => {
        if (open) {
            setTabValue(0);
            setFormData({
                employeeCode: '',
                name: '',
                department: 'Administration',
                designation: 'Credit Agent',
                manager: 'None',
                country: 'Canada',
                state: 'Quebec',
                region: '',
                address: '',
                dateOfJoining: '',
                isActive: true,
            });
        }
    }, [open]);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
    };

    const handleSave = () => {
        onSave(formData);
    };

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const handleNextTab = () => {
        setTabValue(prev => Math.min(prev + 1, EMPLOYEE_STEPS.length - 1));
    };

    const handlePreviousTab = () => {
        setTabValue(prev => Math.max(prev - 1, 0));
    };

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
            <AdminComponents.DialogHeaderContainer>
                <AdminComponents.DialogHeader>
                    <Typography variant="h6">Add Employee</Typography>
                    <Box>
                        <Button color="inherit" onClick={handleSave} startIcon={<Save />}>Save</Button>
                        <Button color="inherit" onClick={onClose} startIcon={<Close />}>Exit</Button>
                    </Box>
                </AdminComponents.DialogHeader>
                <AdminComponents.StepperBox>
                    <Stepper alternativeLabel activeStep={tabValue} connector={<AdminComponents.CustomStepConnector />}>
                        {EMPLOYEE_STEPS.map((label, index) => (
                            <Step key={label} completed={tabValue > index}>
                                <StepLabel StepIconComponent={StepIcon}>{""}</StepLabel>
                            </Step>
                        ))}
                    </Stepper>
                </AdminComponents.StepperBox>
                <AdminComponents.TabsContainer>
                    <AdminComponents.StyledTabs value={tabValue} onChange={handleTabChange} variant="fullWidth">
                        {EMPLOYEE_STEPS.map((label) => <AdminComponents.StyledTab key={label} label={label} />)}
                    </AdminComponents.StyledTabs>
                </AdminComponents.TabsContainer>
            </AdminComponents.DialogHeaderContainer>

            <AdminComponents.ScrollableDialogContent>
                <TabPanel value={tabValue} index={0}>
                    <AdminComponents.FormSection>
                        <AdminComponents.FormSectionTitle>Employee Information</AdminComponents.FormSectionTitle>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}><TextField required label="Employee Code" name="employeeCode" value={formData.employeeCode || ''} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} md={6}><TextField required label="Name" name="name" value={formData.name || ''} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} md={6}>
                                <FormControl required fullWidth variant="outlined">
                                    <InputLabel>Department</InputLabel>
                                    <Select native label="Department" value={formData.department || ''} name="department" onChange={handleChange}>
                                        {employeeDropdownData.departments.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <FormControl required fullWidth variant="outlined">
                                    <InputLabel>Designation</InputLabel>
                                    <Select native label="Designation" value={formData.designation || ''} name="designation" onChange={handleChange}>
                                        {employeeDropdownData.designations.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <FormControl fullWidth variant="outlined">
                                    <InputLabel>Manager</InputLabel>
                                    <Select native label="Manager" value={formData.manager || ''} name="manager" onChange={handleChange}>
                                         {employeeDropdownData.managers.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} md={6}><TextField label="Date of Joining" type="date" name="dateOfJoining" value={formData.dateOfJoining || ''} onChange={handleChange} fullWidth variant="outlined" InputLabelProps={{ shrink: true }} /></Grid>
                        </Grid>
                    </AdminComponents.FormSection>
                    
                    <AdminComponents.FormSection>
                        <AdminComponents.FormSectionTitle>Location & Contact</AdminComponents.FormSectionTitle>
                        <Grid container spacing={2}>
                             <Grid item xs={12}><TextField required label="Address" name="address" value={formData.address || ''} onChange={handleChange} fullWidth multiline rows={2} variant="outlined" /></Grid>
                             <Grid item xs={12} sm={6} md={3}>
                                <FormControl required fullWidth variant="outlined">
                                    <InputLabel>Country</InputLabel>
                                    <Select native label="Country" value={formData.country || ''} name="country" onChange={handleChange}>
                                        {employeeDropdownData.countries.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                                <FormControl required fullWidth variant="outlined">
                                    <InputLabel>State</InputLabel>
                                    <Select native label="State" value={formData.state || ''} name="state" onChange={handleChange}>
                                        {employeeDropdownData.states.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Region" name="region" value={formData.region || ''} onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Location" name="location" onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Zip Code" name="zipCode" onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Landline" name="landline" onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Mobile" name="mobile" onChange={handleChange} fullWidth variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Email" name="email" onChange={handleChange} fullWidth variant="outlined" /></Grid>
                        </Grid>
                    </AdminComponents.FormSection>

                     <AdminComponents.FormSection>
                        <AdminComponents.FormSectionTitle>Status</AdminComponents.FormSectionTitle>
                        <Grid container spacing={2}>
                           <Grid item xs={12} md={6}><TextField label="Date of Relieving" type="date" name="dateOfRelieving" onChange={handleChange} fullWidth variant="outlined" InputLabelProps={{ shrink: true }} /></Grid>
                           <AdminComponents.CenteredGridItem item xs={12} md={6}><FormControlLabel control={<Checkbox name="isActive" checked={formData.isActive || false} onChange={handleChange} />} label="Is Active?" /></AdminComponents.CenteredGridItem>
                        </Grid>
                    </AdminComponents.FormSection>
                </TabPanel>
                <TabPanel value={tabValue} index={1}>
                    <DialogTabView data={mockSkillsData} columns={skillsColumns} entityName="Skill" onAdd={() => console.log("Add Skill clicked")} />
                </TabPanel>
                <TabPanel value={tabValue} index={2}>
                     <DialogTabView 
                        data={mockEmployeeBranchesData} 
                        columns={employeeBranchesColumns} 
                        entityName="Branch Association"
                        onAdd={() => console.log("Add Branch Association clicked")}
                    />
                </TabPanel>
            </AdminComponents.ScrollableDialogContent>
            <AdminComponents.DialogFooter>
                 <AdminComponents.FooterButtonContainer>
                    <Button variant="outlined" onClick={handlePreviousTab} disabled={tabValue === 0} startIcon={<NavigateBefore />}>Previous</Button>
                    <AdminComponents.SpacedButton variant="outlined" onClick={handleNextTab} disabled={tabValue === EMPLOYEE_STEPS.length - 1} endIcon={<NavigateNext />}>Next</AdminComponents.SpacedButton>
                </AdminComponents.FooterButtonContainer>
                <AdminComponents.FooterSpacer />
                <AdminComponents.FooterButtonContainer>
                    <Button variant="contained" startIcon={<Save />} onClick={handleSave}>Save & Close</Button>
                    <AdminComponents.SpacedButton variant="outlined" startIcon={<Close />} onClick={onClose}>Cancel</AdminComponents.SpacedButton>
                </AdminComponents.FooterButtonContainer>
            </AdminComponents.DialogFooter>
        </Dialog>
    );
};

const BrandDialog = ({ open, onClose, onSave }) => {
    const [brandName, setBrandName] = useState('');

    const handleSave = () => {
        if (brandName) {
            onSave({ name: brandName, id: Date.now(), status: 'Active' });
            onClose();
        }
    };

    useEffect(() => {
        if (open) {
            setBrandName('');
        }
    }, [open]);

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="xs">
            <AdminComponents.DialogHeader>
                <Typography variant="h6">Add Brand Association</Typography>
            </AdminComponents.DialogHeader>
            <AdminComponents.DialogContent sx={{ p: 4 }}>
                <FormControl fullWidth variant="outlined">
                    <InputLabel id="brand-name-label">Brand Name</InputLabel>
                    <Select
                        labelId="brand-name-label"
                        value={brandName}
                        onChange={(e) => setBrandName(e.target.value)}
                        label="Brand Name"
                    >
                        <MenuItem value=""><em>--Select--</em></MenuItem>
                        {brandNameOptions.map(name => <MenuItem key={name} value={name}>{name}</MenuItem>)}
                    </Select>
                </FormControl>
            </AdminComponents.DialogContent>
            <AdminComponents.DialogActions>
                <Button onClick={onClose}>Cancel</Button>
                <Button onClick={handleSave} variant="contained">Save</Button>
            </AdminComponents.DialogActions>
        </Dialog>
    );
};

const RelationDialog = ({ open, onClose, onSave }) => {
    const [relation, setRelation] = useState({ name: '', partnerType: '' });

    const handleSave = () => {
        if (relation.name && relation.partnerType) {
            onSave({ ...relation, id: Date.now(), status: 'Active' });
            onClose();
        }
    };
    
    const handleChange = (e) => {
        const { name, value } = e.target;
        setRelation(prev => ({...prev, [name]: value}));
    };

    useEffect(() => {
        if (open) {
            setRelation({ name: '', partnerType: '' });
        }
    }, [open]);

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="xs">
            <AdminComponents.DialogHeader>
                <Typography variant="h6">Add Company Relation</Typography>
            </AdminComponents.DialogHeader>
            <AdminComponents.DialogContent sx={{ p: 4, display: 'flex', flexDirection: 'column', gap: 3 }}>
                <FormControl fullWidth variant="outlined">
                    <InputLabel id="partner-company-label">Partner/Company</InputLabel>
                    <Select
                        labelId="partner-company-label"
                        name="name"
                        value={relation.name}
                        onChange={handleChange}
                        label="Partner/Company"
                    >
                        <MenuItem value=""><em>--Select--</em></MenuItem>
                        {partnerCompanyOptions.map(name => <MenuItem key={name} value={name}>{name}</MenuItem>)}
                    </Select>
                </FormControl>
                <FormControl fullWidth variant="outlined">
                    <InputLabel id="partner-type-label">Partner Type</InputLabel>
                    <Select
                        labelId="partner-type-label"
                        name="partnerType"
                        value={relation.partnerType}
                        onChange={handleChange}
                        label="Partner Type"
                    >
                        <MenuItem value=""><em>--Select--</em></MenuItem>
                        {partnerTypeOptions.map(type => <MenuItem key={type} value={type}>{type}</MenuItem>)}
                    </Select>
                </FormControl>
            </AdminComponents.DialogContent>
            <AdminComponents.DialogActions>
                <Button onClick={onClose}>Cancel</Button>
                <Button onClick={handleSave} variant="contained">Save</Button>
            </AdminComponents.DialogActions>
        </Dialog>
    );
};


const CustomerDialog = ({ open, onClose, customerData, mode, onSave, onAddBranch, onAddEmployee }) => {
    const [formData, setFormData] = useState({});
    const [tabValue, setTabValue] = useState(0);

    const [relations, setRelations] = useState(mockRelations);
    const [brands, setBrands] = useState(mockBrands);
    const [isRelationDialogOpen, setIsRelationDialogOpen] = useState(false);
    const [isBrandDialogOpen, setIsBrandDialogOpen] = useState(false);

    useEffect(() => {
        setFormData(customerData || {});
        setRelations(mockRelations);
        setBrands(mockBrands);
        setTabValue(0); // Reset to first tab when dialog opens/re-opens
    }, [customerData, open]);

    const isViewOnly = mode === 'view';
    const title = mode === 'add' ? 'Add Customer' : (mode === 'edit' ? 'Edit Customer' : 'View Customer');

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
    };

    const handleSave = () => {
        onSave({ ...formData, isDraft: false });
        onClose();
    };

    const handleSaveDraft = () => {
        onSave({ ...formData, isDraft: true });
        onClose();
    };


    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const handleNextTab = () => {
        setTabValue(prev => Math.min(prev + 1, CUSTOMER_STEPS.length - 1));
    };

    const handlePreviousTab = () => {
        setTabValue(prev => Math.max(prev - 1, 0));
    };

    const handleSaveRelation = (newRelation) => {
        setRelations(prev => [...prev, newRelation]);
    };
    
    const handleSaveBrand = (newBrand) => {
        setBrands(prev => [...prev, newBrand]);
    };

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
            <AdminComponents.DialogHeaderContainer>
                <AdminComponents.DialogHeader>
                    <AdminComponents.DialogProgressContainer>
                        <Typography variant="h6">{title}</Typography>
                    </AdminComponents.DialogProgressContainer>
                    <Box>
                        {!isViewOnly && <Button color="inherit" startIcon={<Save />} onClick={handleSave}>Save</Button>}
                        <Button color="inherit" startIcon={<Close />} onClick={onClose}>Exit</Button>
                    </Box>
                </AdminComponents.DialogHeader>

                <AdminComponents.StepperBox>
                    <Stepper alternativeLabel activeStep={tabValue} connector={<AdminComponents.CustomStepConnector />}>
                        {CUSTOMER_STEPS.map((label, index) => (
                            <Step key={label} completed={tabValue > index}>
                                <StepLabel StepIconComponent={StepIcon}>{""}</StepLabel>
                            </Step>
                        ))}
                    </Stepper>
                </AdminComponents.StepperBox>
                <AdminComponents.TabsContainer>
                    <AdminComponents.StyledTabs
                        value={tabValue}
                        onChange={handleTabChange}
                        variant="fullWidth"
                        aria-label="customer details tabs"
                    >
                        {CUSTOMER_STEPS.map((label) => (
                            <AdminComponents.StyledTab key={label} label={label} />
                        ))}
                    </AdminComponents.StyledTabs>
                </AdminComponents.TabsContainer>
            </AdminComponents.DialogHeaderContainer>

            <AdminComponents.ScrollableDialogContent>
                <TabPanel value={tabValue} index={0}>
                    <AdminComponents.FormSection>
                        <AdminComponents.FormSectionTitle>Primary Information</AdminComponents.FormSectionTitle>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}><TextField label="Name *" name="name" value={formData.name || ''} onChange={handleChange} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                            <Grid item xs={12} md={6}><TextField label="Short Name *" name="shortName" value={formData.shortName || ''} onChange={handleChange} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                            <Grid item xs={12}><TextField label="Address *" name="address" value={formData.address || ''} onChange={handleChange} fullWidth multiline rows={2} disabled={isViewOnly} variant="outlined" /></Grid>
                        </Grid>
                    </AdminComponents.FormSection>

                    <AdminComponents.FormSection>
                        <AdminComponents.FormSectionTitle>Company Details</AdminComponents.FormSectionTitle>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={6} md={3}>
                                <FormControl fullWidth disabled={isViewOnly} variant="outlined">
                                    <InputLabel>Parent Company</InputLabel>
                                    <Select native label="Parent Company" value={formData.parentCompany || ''} name="parentCompany" onChange={handleChange}>
                                        <option aria-label="None" value="" />
                                        {dialogDropdownData.parentCompanies.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                                <FormControl fullWidth disabled={isViewOnly} variant="outlined">
                                    <InputLabel>Company Type *</InputLabel>
                                    <Select native label="Company Type *" value={formData.companyType || ''} name="companyType" onChange={handleChange}>
                                        <option aria-label="None" value="" />
                                        {dialogDropdownData.companyTypes.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                                <FormControl fullWidth disabled={isViewOnly} variant="outlined">
                                    <InputLabel>Industry</InputLabel>
                                    <Select native label="Industry" value={formData.industry || ''} name="industry" onChange={handleChange}>
                                        <option aria-label="None" value="" />
                                        {dialogDropdownData.industries.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                                <FormControl fullWidth disabled={isViewOnly} variant="outlined">
                                    <InputLabel>Currency *</InputLabel>
                                    <Select native label="Currency *" value={formData.currency || ''} name="currency" onChange={handleChange}>
                                        <option aria-label="None" value="" />
                                        {dialogDropdownData.currencies.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                        </Grid>
                    </AdminComponents.FormSection>

                    <AdminComponents.FormSection>
                        <AdminComponents.FormSectionTitle>Configuration</AdminComponents.FormSectionTitle>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Ordering Cost *" name="orderingCost" value={formData.orderingCost || ''} onChange={handleChange} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Work Order Cushion Hours *" name="workOrderCushion" value={formData.workOrderCushion || ''} onChange={handleChange} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Inventory Carrying Factor (%) *" name="inventoryFactor" value={formData.inventoryFactor || ''} onChange={handleChange} fullWidth InputProps={{ endAdornment: '%' }} disabled={isViewOnly} variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}><TextField label="Logo Name" name="logoName" value={formData.logoName || ''} onChange={handleChange} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                            <Grid item xs={12} sm={6} md={3}>
                                <FormControl fullWidth disabled={isViewOnly} variant="outlined">
                                    <InputLabel>Company Theme *</InputLabel>
                                    <Select native label="Company Theme *" value={formData.companyTheme || ''} name="companyTheme" onChange={handleChange}>
                                        <option aria-label="None" value="" />
                                        {dialogDropdownData.companyThemes.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                                <FormControl fullWidth disabled={isViewOnly} variant="outlined">
                                    <InputLabel>Company Font</InputLabel>
                                    <Select native label="Company Font" value={formData.companyFont || ''} name="companyFont" onChange={handleChange}>
                                        <option aria-label="None" value="" />
                                        {dialogDropdownData.companyFonts.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </Select>
                                </FormControl>
                            </Grid>
                        </Grid>
                    </AdminComponents.FormSection>

                    <AdminComponents.FormSection>
                        <AdminComponents.FormSectionTitle>Notes & Status</AdminComponents.FormSectionTitle>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={9}><TextField label="Remarks" name="remarks" value={formData.remarks || ''} onChange={handleChange} fullWidth multiline rows={3} disabled={isViewOnly} variant="outlined" /></Grid>
                            <AdminComponents.CenteredGridItem item xs={12} md={3}><FormControlLabel control={<Checkbox name="isActive" checked={formData.isActive || false} onChange={handleChange} disabled={isViewOnly} />} label="Is Active?" /></AdminComponents.CenteredGridItem>
                        </Grid>
                    </AdminComponents.FormSection>

                </TabPanel>
                <TabPanel value={tabValue} index={1}>
                    <DialogTabView data={mockBranches} columns={branchColumns} entityName="Branch" onAdd={onAddBranch} />
                </TabPanel>
                <TabPanel value={tabValue} index={2}>
                    <DialogTabView data={relations} columns={relationColumns} entityName="Relation" onAdd={() => setIsRelationDialogOpen(true)} />
                </TabPanel>
                <TabPanel value={tabValue} index={3}>
                    <DialogTabView data={brands} columns={brandColumns} entityName="Brand" onAdd={() => setIsBrandDialogOpen(true)} />
                </TabPanel>
                <TabPanel value={tabValue} index={4}>
                    <DialogTabView 
                        data={mockEmployees} 
                        columns={employeeColumns} 
                        entityName="Employee" 
                        onAdd={onAddEmployee} 
                    />
                </TabPanel>
            </AdminComponents.ScrollableDialogContent>
            <AdminComponents.DialogFooter>
                <AdminComponents.FooterButtonContainer>
                    <Button variant="outlined" onClick={handlePreviousTab} disabled={tabValue === 0} startIcon={<NavigateBefore />}>
                        Previous
                    </Button>
                    <AdminComponents.SpacedButton variant="outlined" onClick={handleNextTab} disabled={tabValue === CUSTOMER_STEPS.length - 1} endIcon={<NavigateNext />}>
                        Next
                    </AdminComponents.SpacedButton>
                </AdminComponents.FooterButtonContainer>
                <AdminComponents.FooterSpacer />
                <AdminComponents.FooterButtonContainer>
                    {!isViewOnly && <Button variant="outlined" onClick={handleSaveDraft}>Save as Draft</Button>}
                    {!isViewOnly && <AdminComponents.SpacedButton variant="contained" startIcon={<Save />} onClick={handleSave}>Save & Close</AdminComponents.SpacedButton>}
                    <AdminComponents.SpacedButton variant="outlined" startIcon={<Close />} onClick={onClose}>Cancel</AdminComponents.SpacedButton>
                </AdminComponents.FooterButtonContainer>
            </AdminComponents.DialogFooter>

            <RelationDialog 
                open={isRelationDialogOpen}
                onClose={() => setIsRelationDialogOpen(false)}
                onSave={handleSaveRelation}
            />
             <BrandDialog 
                open={isBrandDialogOpen}
                onClose={() => setIsBrandDialogOpen(false)}
                onSave={handleSaveBrand}
            />
        </Dialog>
    );
};


const ActionButtons = ({ customer, onView, onEdit, onDelete, onViewProducts, isCondensed }) => {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);

    const handleClick = (event) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget);
    };

    const handleClose = (event) => {
        event.stopPropagation();
        setAnchorEl(null);
    };

    const handleAction = (action, event) => {
        handleClose(event);
        action();
    }

    if (isCondensed) {
        return (
            <Box onClick={e => e.stopPropagation()}>
                <IconButton
                    aria-label="more"
                    id="long-button"
                    aria-controls={open ? 'long-menu' : undefined}
                    aria-expanded={open ? 'true' : undefined}
                    aria-haspopup="true"
                    onClick={handleClick}
                    size="small"
                >
                    <MoreVert />
                </IconButton>
                <Menu
                    id="long-menu"
                    MenuListProps={{
                        'aria-labelledby': 'long-button',
                    }}
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                >
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onView(customer), e)}>
                        <Visibility fontSize="small" /> View
                    </AdminComponents.ActionMenuItem>
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onViewProducts(customer), e)}>
                        <Assessment fontSize="small" /> View Products
                    </AdminComponents.ActionMenuItem>
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onEdit(customer), e)}>
                        <Edit fontSize="small" /> Edit
                    </AdminComponents.ActionMenuItem>
                    <AdminComponents.ErrorMenuItem onClick={(e) => handleAction(() => onDelete([customer.id]), e)}>
                        <Delete fontSize="small" /> Delete
                    </AdminComponents.ErrorMenuItem>
                </Menu>
            </Box>
        );
    }

    return (
        <Box onClick={e => e.stopPropagation()}>
            <IconButton size="small" onClick={() => onView(customer)} title="View Details"><Visibility fontSize="small" /></IconButton>
            <IconButton size="small" onClick={() => onEdit(customer)} title="Edit"><Edit fontSize="small" /></IconButton>
            <IconButton size="small" color="error" onClick={() => onDelete([customer.id])} title="Delete"><Delete fontSize="small" /></IconButton>
        </Box>
    );
};

const CustomerCard = ({ customer, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, onViewProducts, isCondensed }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(customer.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ customer, onView, onEdit, onDelete, onViewProducts, isCondensed }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="h6" component="div" noWrap>{customer.name}</Typography>
            </Box>
            <AdminComponents.IndustryTypography color="text.secondary">{customer.industry}</AdminComponents.IndustryTypography>
            <Box display="flex" alignItems="center" gap={1} mt={1}>
                <AdminComponents.StatusBadge ownerState={{ status: customer.status }} label={customer.status} size="small" />
                <a href="#" style={{ color: '#1976d2', textDecoration: 'underline', fontSize: '0.98em', marginLeft: 8 }}>View Products</a>
            </Box>
            <AdminComponents.CardDivider />
            <AdminComponents.CardDetailsGrid>
                <AdminComponents.CardDetailLabel variant="body2">Tier:</AdminComponents.CardDetailLabel>
                <Typography variant="body2" noWrap>{customer.type}</Typography>

                <AdminComponents.CardDetailLabel variant="body2">Type:</AdminComponents.CardDetailLabel>
                <Typography variant="body2" noWrap>{CUSTOMER_TYPE_LABELS[customer.customerType]}</Typography>

                <AdminComponents.CardDetailLabel variant="body2">Contact:</AdminComponents.CardDetailLabel>
                <Typography variant="body2" noWrap>{customer.primaryContact.name}</Typography>
            </AdminComponents.CardDetailsGrid>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const CustomerCompactCard = ({ customer, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, onViewProducts, isCondensed }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(customer.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ customer, onView, onEdit, onDelete, onViewProducts, isCondensed }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{customer.name}</Typography>
            </Box>
            <Typography variant="caption" color="text.secondary">{customer.industry}</Typography>
            <Box display="flex" alignItems="center" gap={1} mt={1}>
                <AdminComponents.StatusBadge ownerState={{ status: customer.status }} label={customer.status} size="small" />
                <a href="#" style={{ color: '#1976d2', textDecoration: 'underline', fontSize: '0.98em', marginLeft: 8 }}>View Products</a>
            </Box>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{customer.type}</Typography>
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const CustomerListItem = ({ customer, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, onViewProducts, isCondensed }) => (
    <AdminComponents.ListItemCard isSelected={isSelected}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(customer.id)} onClick={e => e.stopPropagation()} />
            <Box display="flex" alignItems="center" gap={1}>
                <Box>
                    <Typography fontWeight="bold">{customer.name}</Typography>
                    <Typography variant="body2" color="text.secondary">{customer.industry}</Typography>
                </Box>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
                <AdminComponents.StatusBadge ownerState={{ status: customer.status }} label={customer.status} size="small" />
                <a
                    href="#"
                    style={{ color: '#1976d2', textDecoration: 'underline', fontSize: '0.98em', marginLeft: 8 }}
                    onClick={(e) => { e.preventDefault(); e.stopPropagation(); onViewProducts(customer); }}
                >
                    View Products
                </a>
            </Box>
            <Typography variant="body2">{customer.type}</Typography>
            <AdminComponents.ListItemActions>
                <ActionButtons {...{ customer, onView, onEdit, onDelete, onViewProducts, isCondensed }} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const CustomerTable = ({ customers, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, addLog, groupByKeys, onDelete, onEdit, onView, onViewProducts, isCondensed }) => {
    const dragItemIndex = useRef(null);
    const dragOverItemIndex = useRef(null);

    const renderCellContent = (cust, colKey) => {
        if (colKey === 'status') return <AdminComponents.StatusBadge ownerState={{ status: cust.status }} label={cust.status} size="small" />;

        const value = colKey.includes('.') ? colKey.split('.').reduce((o, i) => o?.[i], cust) : cust[colKey];

        if (colKey === 'name') {
            return (
                <AdminComponents.ClickableTypography component="span" onClick={(e) => { e.stopPropagation(); onRowClick(cust); }}>
                    {value}
                </AdminComponents.ClickableTypography>
            );
        }
        return value || '-';
    };

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        addLog({ user: 'Admin', action: 'reordered columns' });
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    const renderGroupedRows = (data, keys, level = 0) => {
        if (!keys.length || !data.length) {
            return data.map(cust => (
                <TableRow key={cust.id} hover selected={selectedId === cust.id}>
                    <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(cust.id)} onChange={() => onSelectOne(cust.id)} /></TableCell>
                    {columnOrder.map(colKey => (
                        <AdminComponents.ContentTableCell key={colKey} onClick={() => onRowClick(cust)}>
                            {renderCellContent(cust, colKey)}
                        </AdminComponents.ContentTableCell>
                    ))}
                    <AdminComponents.ActionTableCell align="center" onClick={() => onRowClick(cust)}>
                        <ActionButtons {...{ customer: cust, onView, onEdit, onDelete, onViewProducts, isCondensed }} />
                    </AdminComponents.ActionTableCell>
                </TableRow>
            ));
        }

        const currentKey = keys[0];
        const remainingKeys = keys.slice(1);
        const groupLabel = ALL_COLUMNS.find(c => c.key === currentKey)?.label;

        const grouped = data.reduce((acc, item) => {
            const groupValue = String(item[currentKey] || 'N/A');
            if (!acc[groupValue]) acc[groupValue] = [];
            acc[groupValue].push(item);
            return acc;
        }, {});

        return Object.entries(grouped).map(([groupValue, items]) => (
            <React.Fragment key={`${level}-${groupValue}`}>
                <AdminComponents.GroupHeaderRow>
                    <AdminComponents.GroupHeaderCell
                        colSpan={columnOrder.length + 2}
                        style={{ paddingLeft: theme.spacing(2 + level * 2) }}
                    >
                        <strong>{groupLabel}:</strong> {groupValue} ({items.length})
                    </AdminComponents.GroupHeaderCell>
                </AdminComponents.GroupHeaderRow>
                {renderGroupedRows(items, remainingKeys, level + 1)}
            </React.Fragment>
        ));
    };


    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <AdminComponents.ResponsiveTable stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < customers.length} checked={customers.length > 0 && selectedIds.length === customers.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, index) => (
                            <AdminComponents.DraggableHeaderCell
                                key={colKey}
                                draggable
                                onDragStart={() => (dragItemIndex.current = index)}
                                onDragEnter={() => (dragOverItemIndex.current = index)}
                                onDrop={handleDrop}
                                onDragOver={(e) => e.preventDefault()}
                                sortDirection={sortColumn === colKey ? sortDirection : false}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </AdminComponents.DraggableHeaderCell>
                        ))}
                        <AdminComponents.ActionTableCell align="center">Actions</AdminComponents.ActionTableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {renderGroupedRows(customers, groupByKeys)}
                </TableBody>
            </AdminComponents.ResponsiveTable>
        </AdminComponents.TableViewContainer>
    );
};

const CustomerGraph = ({ customer, chartType }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);

    useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && customer) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['EE', 'EN', 'NN'],
                    datasets: [{
                        label: 'Customer Type Engagement (Mock)',
                        data: [Math.random() * 100, Math.random() * 100, Math.random() * 100],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${customer.name} - Engagement Analysis` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [customer, chartType]);

    return (
        <>
            {customer ? (
                <AdminComponents.GraphCanvasContainer>
                    <canvas ref={chartRef}></canvas>
                </AdminComponents.GraphCanvasContainer>
            ) : (
                <AdminComponents.CenteredMessage>
                    <Typography>Select a customer to see graph</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <List disablePadding>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <ListItemText
                            primary={
                                <AdminComponents.ActivityLogTextContainer>
                                    <Typography variant="body2" component="span" color="text.secondary">
                                        <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                        {' '}{log.action}{' '}
                                        {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        {log.timestamp}
                                    </Typography>
                                </AdminComponents.ActivityLogTextContainer>
                            }
                        />
                    </AdminComponents.ActivityLogListItem>
                ))}
            </List>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

// --- MAIN APP COMPONENT ---
const Customers = () => {
    const [customers, setCustomers] = useState(initialCustomers);
    const [selectedCustomer, setSelectedCustomer] = useState(null);
    const [modalState, setModalState] = useState({ isOpen: false, customer: null, mode: 'view' });
    const [viewProductsModal, setViewProductsModal] = useState({ isOpen: false, customer: null });
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new view', target: 'Customer Satisfaction Overview', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'Analyst', action: 'Updated a view', target: 'Sales Pipeline Performance', timestamp: '3/16/2023, 9:00:00 AM' },
        { user: 'Marketing Lead', action: 'Deleted a view', target: 'Old Marketing Data', timestamp: '3/17/2023, 1:45:00 PM' },
    ]);
    const [viewMode, setViewMode] = useState('cards');
    const [sortColumn, setSortColumn] = useState('name');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [groupByKeys, setGroupByKeys] = useState([]);
    const [isGraphVisible, setIsGraphVisible] = useState(false);
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [summaryFilter, setSummaryFilter] = useState(null); // State for summary card filter
    const [chartType, setChartType] = useState('bar');
    const [isDraftsDialogOpen, setIsDraftsDialogOpen] = useState(false);
    const [draftDeleteConfirmation, setDraftDeleteConfirmation] = useState({ isOpen: false, draftId: null });
    const [isBranchModalOpen, setIsBranchModalOpen] = useState(false);
    const [isEmployeeModalOpen, setIsEmployeeModalOpen] = useState(false);


    // Advanced Search State
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });

    const quickFilterOptions = useMemo(() => {
        const industries = [...new Set(customers.map(c => c.industry))];
        const statuses = [...new Set(customers.map(c => c.status))];
        return [...statuses, ...industries];
    }, [customers]);

    const processedCustomers = useMemo(() => {
        let current = customers.filter(c => !c.isDraft);

        // Filter by summary card selection
        if (summaryFilter) {
            current = current.filter(c => c.status === summaryFilter);
        }

        // Simple search term filter
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(c =>
                Object.values(c).some(val =>
                    String(val).toLowerCase().includes(term)
                ) ||
                Object.values(c.primaryContact).some(val =>
                    String(val).toLowerCase().includes(term)
                )
            );
        }

        // Advanced filters
        if (activeFilters.length > 0) {
            current = current.filter(customer => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const customerValue = String(field.includes('.') ? field.split('.').reduce((o, i) => o?.[i], customer) : customer[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();

                    switch (operator) {
                        case 'Equals': return customerValue === filterValue;
                        case 'Not Equals': return customerValue !== filterValue;
                        case 'Contains': return customerValue.includes(filterValue);
                        case 'Starts With': return customerValue.startsWith(filterValue);
                        case 'Ends With': return customerValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }

        // Sorting
        return [...current].sort((a, b) => {
            const valA = sortColumn.includes('.') ? sortColumn.split('.').reduce((o, i) => o?.[i], a) : a[sortColumn];
            const valB = sortColumn.includes('.') ? sortColumn.split('.').reduce((o, i) => o?.[i], b) : b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? (valA || 0) - (valB || 0) : (valB || 0) - (valA || 0);
        });
    }, [customers, searchTerm, activeFilters, sortColumn, sortDirection, summaryFilter]);

    const displayCustomer = useMemo(() => {
        const isSelectedVisible = processedCustomers.some(c => c.id === selectedCustomer?.id);
        if (isSelectedVisible) return selectedCustomer;
        return processedCustomers.length > 0 ? processedCustomers[0] : null;
    }, [processedCustomers, selectedCustomer]);

    const addLog = (logEntry) => {
        const timestamp = new Date().toLocaleString();
        setActivityLog(prev => [{ ...logEntry, timestamp }, ...prev].slice(0, 10));
    };

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const confirmDelete = () => {
        addLog({ user: 'Admin', action: `Deleted ${deleteConfirmation.idsToDelete.length} customer(s).` });
        setDeleteConfirmation({ isOpen: false, idsToDelete: [] });
    };

    // View Products handlers
    const handleViewProducts = (customer) => {
        setViewProductsModal({ isOpen: true, customer });
    };

    const handleCloseViewProducts = () => {
        setViewProductsModal({ isOpen: false, customer: null });
    };

    const handleOpenModal = (customer, mode) => {
        const customerData = customer || { name: '', status: 'Active', isActive: true, industry: 'Technology' };
        setModalState({ isOpen: true, customer: customerData, mode });
    };

    const handleCloseModal = () => setModalState({ isOpen: false, customer: null, mode: 'view' });

    const handleSaveCustomer = (customerData) => {
        if (modalState.mode === 'add') {
            const newCustomer = { ...customerData, id: Date.now(), primaryContact: { name: '', email: '' } }; // Add mock contact
            setCustomers(prev => [newCustomer, ...prev]);
            addLog({ user: 'Admin', action: `Saved new ${customerData.isDraft ? 'draft' : 'customer'}: ${newCustomer.name}` });
        } else {
            setCustomers(prev => prev.map(c => c.id === customerData.id ? { ...c, ...customerData } : c));
            const logAction = customerData.isDraft ? `Saved changes to draft: ${customerData.name}` : `Finalized customer: ${customerData.name}`;
            addLog({ user: 'Admin', action: logAction });
        }
        handleCloseModal();
    };

    const handleSelectDraft = (draft) => {
        setIsDraftsDialogOpen(false);
        handleOpenModal(draft, 'edit');
    };

    const handleRequestDeleteDraft = (draftId) => {
        setDraftDeleteConfirmation({ isOpen: true, draftId });
    };

    const handleCancelDeleteDraft = () => {
        setDraftDeleteConfirmation({ isOpen: false, draftId: null });
    };

    const handleConfirmDeleteDraft = () => {
        const { draftId } = draftDeleteConfirmation;
        if (!draftId) return;

        const draftToDelete = customers.find(c => c.id === draftId);
        if (draftToDelete) {
            setCustomers(prev => prev.filter(c => c.id !== draftId));
            addLog({ user: 'Admin', action: `Deleted draft: ${draftToDelete.name || 'Untitled Draft'}` });
        }
        handleCancelDeleteDraft();
    };

    const handleOpenBranchDialog = () => setIsBranchModalOpen(true);
    const handleCloseBranchDialog = () => setIsBranchModalOpen(false);
    const handleSaveBranch = (branchData) => {
        addLog({ user: 'Admin', action: `Saved new branch: ${branchData.branchName}` });
        // Here you would typically send the data to a server
    };

    const handleOpenEmployeeDialog = () => setIsEmployeeModalOpen(true);
    const handleCloseEmployeeDialog = () => setIsEmployeeModalOpen(false);
    const handleSaveEmployee = (employeeData) => {
        addLog({ user: 'Admin', action: `Saved new employee: ${employeeData.name}` });
        // In a real app, you would save this data
        handleCloseEmployeeDialog();
    };

    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedCustomers.map(c => c.id) : []);
    const handleSelectOne = (id) => {
        const selectedIndex = selectedIds.indexOf(id);
        let newSelectedIds = [];

        if (selectedIndex === -1) {
            newSelectedIds = newSelectedIds.concat(selectedIds, id);
        } else if (selectedIndex === 0) {
            newSelectedIds = newSelectedIds.concat(selectedIds.slice(1));
        } else if (selectedIndex === selectedIds.length - 1) {
            newSelectedIds = newSelectedIds.concat(selectedIds.slice(0, -1));
        } else if (selectedIndex > 0) {
            newSelectedIds = newSelectedIds.concat(
                selectedIds.slice(0, selectedIndex),
                selectedIds.slice(selectedIndex + 1),
            );
        }
        setSelectedIds(newSelectedIds);
    };

    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        if (willBeOpen) {
            setIsGraphVisible(false);
        }
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };

    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;

        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                console.warn("Cannot hide the last column.");
                return;
            }
        } else {
            // Add the column back in its original position
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
        const colLabel = ALL_COLUMNS.find(c => c.key === columnKey)?.label || columnKey;
        addLog({ user: 'Admin', action: `toggled '${colLabel}' column visibility` });
    };

    const handleGraphToggle = () => {
        setIsGraphVisible(prev => !prev);
        setIsSidebarOpen(false);
    };

    const summaryStats = useMemo(() => ({
        total: customers.filter(c => !c.isDraft).length,
        active: customers.filter(c => c.status === 'Active' && !c.isDraft).length,
        inactive: customers.filter(c => c.status === 'Inactive' && !c.isDraft).length,
    }), [customers]);

    // --- Advanced Search Handlers ---
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };

    const handleAddQuickFilter = (value) => {
        const field = ['Active', 'Inactive'].includes(value) ? 'status' : 'industry';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };

    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
        setSummaryFilter(null); // Clear summary filter when applying advanced filters
    };

    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
        setSummaryFilter(null); // Clear summary filter on reset
    };

    const handleSummaryCardClick = (status) => {
        setSummaryFilter(prevStatus => (prevStatus === status ? null : status));
        // Clear other filters for a clean summary view
        setActiveFilters([]);
        setStagedFilters([]);
        setSearchTerm('');
    };

    const handleGroupByChange = (key) => {
        setGroupByKeys(prev =>
            prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
        );
    };

    const renderCurrentView = () => {
        const isCondensed = isSidebarOpen || isGraphVisible;
        const viewProps = {
            onDelete: handleDeleteRequest,
            onEdit: (customer) => handleOpenModal(customer, 'edit'),
            onView: (customer) => handleOpenModal(customer, 'view'),
            onViewProducts: handleViewProducts,
        };

        const isSelected = (id) => selectedIds.indexOf(id) !== -1;

        return (
            <AdminComponents.ViewContainer>
                {processedCustomers.length > 0 ? (
                    <>
                        {viewMode === 'cards' && <AdminComponents.GridView>{processedCustomers.map(cust => <CustomerCard key={cust.id} customer={cust} isSelected={displayCustomer?.id === cust.id} onSelect={handleSelectOne} isChecked={isSelected(cust.id)} {...viewProps} isCondensed={false} />)}</AdminComponents.GridView>}
                        {viewMode === 'grid' && (
                            <CustomerTable
                                customers={processedCustomers}
                                onRowClick={setSelectedCustomer}
                                onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                                sortColumn={sortColumn}
                                sortDirection={sortDirection}
                                selectedId={displayCustomer?.id}
                                selectedIds={selectedIds}
                                onSelectAll={handleSelectAll}
                                onSelectOne={handleSelectOne}
                                columnOrder={columnOrder}
                                setColumnOrder={setColumnOrder}
                                addLog={addLog}
                                groupByKeys={groupByKeys}
                                isCondensed={isCondensed}
                                {...viewProps}
                            />
                        )}
                        {viewMode === 'compact' && <AdminComponents.CompactView>{processedCustomers.map(cust => <CustomerCompactCard key={cust.id} customer={cust} isSelected={displayCustomer?.id === cust.id} onSelect={handleSelectOne} isChecked={isSelected(cust.id)} {...viewProps} isCondensed={false} />)}</AdminComponents.CompactView>}
                        {viewMode === 'list' && <AdminComponents.ListView>{processedCustomers.map(cust => <CustomerListItem key={cust.id} customer={cust} isSelected={displayCustomer?.id === cust.id} onSelect={handleSelectOne} isChecked={isSelected(cust.id)} {...viewProps} isCondensed={isCondensed} />)}</AdminComponents.ListView>}
                    </>
                ) : (
                    <AdminComponents.CenteredMessage component={Paper}>
                        <AdminComponents.LargeIcon color="disabled" />
                        <Typography variant="h6">No Matching Customers</Typography>
                        <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                    </AdminComponents.CenteredMessage>
                )}
            </AdminComponents.ViewContainer>
        );
    };

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === null} onClick={() => handleSummaryCardClick(null)}>
                                        <AdminComponents.SummaryAvatar variant="total"><People /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total Customers</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === 'Active'} onClick={() => handleSummaryCardClick('Active')}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.active}</Typography><Typography variant="body2">Active</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === 'Inactive'} onClick={() => handleSummaryCardClick('Inactive')}>
                                        <AdminComponents.SummaryAvatar variant="inactive"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.inactive}</Typography><Typography variant="body2">Inactive</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />} onClick={() => handleOpenModal(null, 'add')}>Add Customer</Button>
                                    <Button variant="outlined" onClick={() => setIsDraftsDialogOpen(true)}>Drafts</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={handleGraphToggle}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedCustomers.length > 0 && selectedIds.length === processedCustomers.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedCustomers.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<Settings />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup
                                        value={chartType}
                                        exclusive
                                        onChange={(e, newType) => newType && setChartType(newType)}
                                        size="small"
                                        fullWidth>
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <CustomerGraph customer={displayCustomer} chartType={chartType} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>

                        <ActivityLog logs={activityLog} />

                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>

                <Drawer
                    variant="persistent"
                    anchor="right"
                    open={isSidebarOpen}
                >
                    <AdminComponents.SidebarContainer>
                        <AdminComponents.SidebarHeader>
                            <Typography variant="h6">
                                {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                            </Typography>
                            <IconButton onClick={() => setIsSidebarOpen(false)}>
                                <Close />
                            </IconButton>
                        </AdminComponents.SidebarHeader>

                        <AdminComponents.SidebarContent>
                            {sidebarMode === 'search' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.QuickFilterContainer>
                                            {quickFilterOptions.map(opt => (
                                                <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                            ))}
                                        </AdminComponents.QuickFilterContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Field</InputLabel>
                                            <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                                {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                            </Select>
                                        </FormControl>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Operator</InputLabel>
                                            <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                                {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                            </Select>
                                        </FormControl>
                                        <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))} />
                                        <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                                            )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {activeFilters.length > 0 ? activeFilters.map(f => (
                                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                            )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                            {sidebarMode === 'grid' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.ColumnActionContainer>
                                            <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                            <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                        </AdminComponents.ColumnActionContainer>
                                        <AdminComponents.ColumnVisibilityContainer>
                                            {ALL_COLUMNS.map(col => (
                                                <FormControlLabel
                                                    key={col.key}
                                                    control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                    label={col.label}
                                                />
                                            ))}
                                        </AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                                <Chip
                                                    key={key}
                                                    label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                                    onDelete={() => handleGroupByChange(key)}
                                                />
                                            )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                        <AdminComponents.ColumnVisibilityContainer>
                                            {ALL_COLUMNS.filter(c => c.groupable).map(col => (
                                                <FormControlLabel
                                                    key={col.key}
                                                    control={
                                                        <Checkbox
                                                            checked={groupByKeys.includes(col.key)}
                                                            onChange={() => handleGroupByChange(col.key)}
                                                        />
                                                    }
                                                    label={col.label}
                                                />
                                            ))}
                                        </AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                        </AdminComponents.SidebarContent>

                        <AdminComponents.SidebarFooter>
                            {sidebarMode === 'search' && (
                                <>
                                    <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
                                    <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
                                </>
                            )}
                            {sidebarMode === 'grid' && (
                                <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                            )}
                        </AdminComponents.SidebarFooter>
                    </AdminComponents.SidebarContainer>
                </Drawer>

                <CustomerDialog
                    open={modalState.isOpen}
                    onClose={handleCloseModal}
                    customerData={modalState.customer}
                    mode={modalState.mode}
                    onSave={handleSaveCustomer}
                    onAddBranch={handleOpenBranchDialog}
                    onAddEmployee={handleOpenEmployeeDialog}
                />

                <DraftsDialog
                    open={isDraftsDialogOpen}
                    onClose={() => setIsDraftsDialogOpen(false)}
                    customers={customers}
                    onSelectDraft={handleSelectDraft}
                    onRequestDelete={handleRequestDeleteDraft}
                />

                <ConfirmationDialog
                    open={draftDeleteConfirmation.isOpen}
                    onClose={handleCancelDeleteDraft}
                    onConfirm={handleConfirmDeleteDraft}
                    title="Confirm Draft Deletion"
                >
                    Are you sure you want to permanently delete this draft? This action cannot be undone.
                </ConfirmationDialog>

                <BranchDialog
                    open={isBranchModalOpen}
                    onClose={handleCloseBranchDialog}
                    onSave={handleSaveBranch}
                />
                <EmployeeDialog
                    open={isEmployeeModalOpen}
                    onClose={handleCloseEmployeeDialog}
                    onSave={handleSaveEmployee}
                />

                <ViewProducts
                    open={viewProductsModal.isOpen}
                    onClose={handleCloseViewProducts}
                    customer={viewProductsModal.customer}
                />

            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Customers;