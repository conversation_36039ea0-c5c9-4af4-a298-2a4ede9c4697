import React, { useState, useMemo } from 'react';
import { NavLink } from 'react-router-dom';
import { Tooltip, TextField, InputAdornment, Menu, MenuItem, ListItemText, ListItemIcon } from '@mui/material';
import {
    HomeOutlined,
    PeopleOutline,
    DescriptionOutlined,
    ShoppingCartOutlined,
    SyncOutlined,
    BusinessCenterOutlined,
    DnsOutlined,
    HandshakeOutlined,
    FlightOutlined,
    HeadsetMicOutlined,
    SettingsOutlined,
    Search,
    KeyboardArrowDown,
    ExpandMore,
    ExpandLess,
} from '@mui/icons-material';
import { AdminComponents } from '../../../styles/theme';


const navItems = [
    { id: 'dashboard', name: 'Dashboard', path: '/admin', icon: <HomeOutlined /> },
    { id: 'customers', name: 'On Boarding', path: '/admin/OnBoarding', icon: <PeopleOutline /> },
    { id: 'contracts', name: 'Contract Management', path: '/admin/contract', icon: <DescriptionOutlined /> },
    { id: 'purchase_orders', name: 'Purchase Orders', path: '/admin/purchase', icon: <ShoppingCartOutlined /> },
    { id: 'invoices', name: 'Invoice', path: '/admin/invoices', icon: <DescriptionOutlined /> },
    { id: 'receipts', name: 'Receipts', path: '/admin/receipts', icon: <SyncOutlined /> },
    {
        id: 'settings',
        name: 'Settings',
        icon: <SettingsOutlined />,
        isParent: true,
        children: [
            { id: 'services', name: 'Professional Services', path: '/admin/professional', icon: <BusinessCenterOutlined /> },
            { id: 'license', name: 'License Management', path: '/admin/license', icon: <DescriptionOutlined /> },
            { id: 'user', name: 'User', path: '/admin/user', icon: <PeopleOutline /> },
            { id: 'server', name: 'Server Maintenance', path: '/admin/server', icon: <DnsOutlined /> },
            { id: 'sla', name: 'SLA', path: '/admin/sla', icon: <HandshakeOutlined /> },
            { id: 'travel', name: 'Travel Details', path: '/admin/travel', icon: <FlightOutlined /> },
            { id: 'support', name: 'Product Support', path: '/admin/support', icon: <HeadsetMicOutlined /> },
        ]
    },
];









// --- Main Navigation Component ---
const AdminNavigation = ({ mode }) => {
    const [isHovered, setIsHovered] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [expandedMenus, setExpandedMenus] = useState(new Set(['settings']));
    const [settingsDropdownAnchor, setSettingsDropdownAnchor] = useState(null);



    const handleSettingsDropdownOpen = (event) => setSettingsDropdownAnchor(event.currentTarget);
    const handleSettingsDropdownClose = () => setSettingsDropdownAnchor(null);

    const handleSettingsDropdownMouseEnter = (event) => {
        setSettingsDropdownAnchor(event.currentTarget);
    };

    const handleSettingsDropdownMouseLeave = () => {
        // Add a small delay to prevent accidental closing when moving between trigger and menu
        setTimeout(() => {
            if (!document.querySelector('.settings-dropdown-menu:hover')) {
                setSettingsDropdownAnchor(null);
            }
        }, 100);
    };

    const toggleMenu = (menuId) => {
        setExpandedMenus(prev => {
            const newSet = new Set(prev);
            if (newSet.has(menuId)) {
                newSet.delete(menuId);
            } else {
                newSet.add(menuId);
            }
            return newSet;
        });
    };

    const filteredNavItems = useMemo(() => {
        if (!searchTerm) return navItems;

        return navItems.filter(item => {
            if (item.isParent) {
                // Check if parent name matches or any child matches
                const parentMatches = item.name.toLowerCase().includes(searchTerm.toLowerCase());
                const childMatches = item.children?.some(child =>
                    child.name.toLowerCase().includes(searchTerm.toLowerCase())
                );
                return parentMatches || childMatches;
            }
            return item.name.toLowerCase().includes(searchTerm.toLowerCase());
        });
    }, [searchTerm]);

    if (mode === 'sidebar') {
        const isCollapsed = !isHovered;
        return (
            <AdminComponents.AdminNavSidebar component="aside" className={`sidebar ${isCollapsed ? 'collapsed' : ''}`} onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
                <AdminComponents.AdminNavSidebarSearch>
                    {!isCollapsed && ( <TextField fullWidth variant="outlined" size="small" placeholder="Search menu..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} InputProps={{ startAdornment: ( <InputAdornment position="start"><Search /></InputAdornment> ), }} /> )}
                </AdminComponents.AdminNavSidebarSearch>
                <AdminComponents.AdminNavSidebarNav component="nav" className="sidebar-nav" isCollapsed={isCollapsed}>
                    {filteredNavItems.map((item) => (
                        <div key={item.id}>
                            {item.isParent ? (
                                <>
                                    <Tooltip title={isCollapsed ? item.name : ''} placement="right">
                                        <div
                                            className={`nav-parent-item ${isCollapsed ? 'collapsed' : ''}`}
                                            onClick={() => !isCollapsed && toggleMenu(item.id)}
                                            role="button"
                                            tabIndex={0}
                                            aria-expanded={expandedMenus.has(item.id)}
                                            aria-controls={`submenu-${item.id}`}
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') {
                                                    e.preventDefault();
                                                    !isCollapsed && toggleMenu(item.id);
                                                }
                                            }}
                                        >
                                            {item.icon}
                                            {!isCollapsed && (
                                                <>
                                                    <span>{item.name}</span>
                                                    <span className="expand-icon">
                                                        {expandedMenus.has(item.id) ? <ExpandLess /> : <ExpandMore />}
                                                    </span>
                                                </>
                                            )}
                                        </div>
                                    </Tooltip>
                                    {!isCollapsed && expandedMenus.has(item.id) && (
                                        <div
                                            className="nav-submenu"
                                            id={`submenu-${item.id}`}
                                            role="menu"
                                            aria-label={`${item.name} submenu`}
                                        >
                                            {item.children?.map((child) => (
                                                <Tooltip key={child.id} title="" placement="right">
                                                    <NavLink
                                                        to={child.path}
                                                        className={({ isActive }) => `nav-child-item ${isActive ? "active" : ""}`}
                                                        end={child.path === '/admin'}
                                                        role="menuitem"
                                                    >
                                                        {child.icon}
                                                        <span>{child.name}</span>
                                                    </NavLink>
                                                </Tooltip>
                                            ))}
                                        </div>
                                    )}
                                </>
                            ) : (
                                <Tooltip key={item.name} title={isCollapsed ? item.name : ''} placement="right">
                                    <NavLink to={item.path} className={({ isActive }) => (isActive ? "active" : "")} end={item.path === '/admin'}>
                                        {item.icon}<span>{item.name}</span>
                                    </NavLink>
                                </Tooltip>
                            )}
                        </div>
                    ))}
                </AdminComponents.AdminNavSidebarNav>
            </AdminComponents.AdminNavSidebar>
        );
    }

    return (
        <AdminComponents.AdminNavTopBar component="nav">
            <AdminComponents.AdminNavTopBarList>
                {navItems.filter(item => !item.isParent).map((item) => (
                    <li key={item.id}>
                        <NavLink
                            to={item.path}
                            className={({ isActive }) => isActive ? "top-nav-link active" : "top-nav-link"}
                            end={item.path === '/admin'}
                        >
                            {item.icon}
                            <span>{item.name}</span>
                        </NavLink>
                    </li>
                ))}

                {/* Settings Dropdown Menu */}
                {navItems.filter(item => item.isParent).map((parentItem) => (
                    <li key={parentItem.id} className="top-nav-dropdown">
                        <button
                            className="top-nav-dropdown-trigger"
                            onClick={handleSettingsDropdownOpen}
                            onMouseEnter={handleSettingsDropdownMouseEnter}
                            onMouseLeave={handleSettingsDropdownMouseLeave}
                        >
                            {parentItem.icon}
                            <span>{parentItem.name}</span>
                            <KeyboardArrowDown className="dropdown-arrow" />
                        </button>

                        <Menu
                            anchorEl={settingsDropdownAnchor}
                            open={Boolean(settingsDropdownAnchor)}
                            onClose={handleSettingsDropdownClose}
                            onMouseLeave={handleSettingsDropdownMouseLeave}
                            anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                            transformOrigin={{ vertical: 'top', horizontal: 'left' }}
                            className="settings-dropdown-menu"
                            MenuListProps={{
                                onMouseLeave: handleSettingsDropdownMouseLeave
                            }}
                        >
                            {parentItem.children?.map((child) => (
                                <MenuItem
                                    key={child.id}
                                    component={NavLink}
                                    to={child.path}
                                    onClick={handleSettingsDropdownClose}
                                    className={({ isActive }) => `settings-dropdown-item ${isActive ? 'active' : ''}`}
                                    end={child.path === '/admin'}
                                >
                                    <ListItemIcon>{child.icon}</ListItemIcon>
                                    <ListItemText primary={child.name} />
                                </MenuItem>
                            ))}
                        </Menu>
                    </li>
                ))}
            </AdminComponents.AdminNavTopBarList>
        </AdminComponents.AdminNavTopBar>
    );
};

export default AdminNavigation;