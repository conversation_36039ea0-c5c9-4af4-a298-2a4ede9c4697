import { createTheme, styled } from '@mui/material/styles';
import {
    Box, Paper, Card, Chip, Typography, ListItem, ListItemIcon, List, ToggleButtonGroup, Table, TableCell, TableContainer as MuiTableContainer, TableRow, CardContent, Divider, Avatar, MenuItem, FormControlLabel, AppBar, Toolbar, InputBase, Badge, Menu, Popover, ListItemText, Button, Modal, Grid, ListSubheader, Link, IconButton, Alert, Stepper, Step, StepLabel, StepConnector, stepConnectorClasses, DialogTitle, DialogContent, DialogActions, LinearProgress, Tabs, Tab
} from '@mui/material';
import { SearchOff, Description, Schedule, Check } from '@mui/icons-material';


// Helper function to get CSS custom property value safely
const getCSSVariable = (variableName) => {
    if (typeof window === 'undefined') {
        return undefined; // SSR fallback
    }
    try {
        const value = getComputedStyle(document.documentElement).getPropertyValue(variableName).trim();
        return value || undefined;
    } catch (error) {
        return undefined;
    }
};

const cssVars = {
    // Colors
    primaryTeal: getCSSVariable('--primary-teal'),
    primaryTealDark: getCSSVariable('--primary-teal-dark'),
    primaryBlue: getCSSVariable('--primary-blue'),
    primaryWhite: getCSSVariable('--primary-white'),
    lightTeal: getCSSVariable('--light-teal'),
    secondaryTealDark: getCSSVariable('--secondary-teal-dark'),
    secondaryBlueLight: getCSSVariable('--secondary-blue-light'),
    secondaryGrayLight: getCSSVariable('--secondary-gray-light'),
    textPrimary: getCSSVariable('--text-primary'),
    textSecondary: getCSSVariable('--text-secondary'),
    textWhite: getCSSVariable('--text-white'),
    textError: getCSSVariable('--text-error'),
    textDisabled: getCSSVariable('--text-disabled'),
    backgroundPrimary: getCSSVariable('--background-primary'),
    backgroundSecondary: getCSSVariable('--background-secondary'),
    backgroundHoverLight: getCSSVariable('--background-hover-light'),
    backgroundAccentLight: getCSSVariable('--background-accent-light'),
    borderColor: getCSSVariable('--border-color'),
    avatarBg: getCSSVariable('--avatar-bg'),
    avatarColor: getCSSVariable('--avatar-color'),
    topbarIcon: getCSSVariable('--topbar-icon'),
    // Status
    successMain: getCSSVariable('--success-main'),
    successDark: getCSSVariable('--success-dark'),
    successLight: getCSSVariable('--success-light'),
    warningMain: getCSSVariable('--warning-main'),
    warningDark: getCSSVariable('--warning-dark'),
    warningLight: getCSSVariable('--warning-light'),
    errorMain: getCSSVariable('--error-main'),
    errorDark: getCSSVariable('--error-dark'),
    errorLight: getCSSVariable('--error-light'),
    infoMain: getCSSVariable('--info-main'),
    infoDark: getCSSVariable('--info-dark'),
    infoLight: getCSSVariable('--info-light'),
    // Shadows
    shadowSm: getCSSVariable('--shadow-sm'),
    shadowMd: getCSSVariable('--shadow-md'),
    shadowLg: getCSSVariable('--shadow-lg'),
    // Radii
    radiusMd: getCSSVariable('--radius-md'),
    radiusLg: getCSSVariable('--radius-lg'),
    radiusXxl: getCSSVariable('--radius-xxl'),
};

export const theme = createTheme({
    palette: {
        primary: {
            main: cssVars.primaryTeal,
            dark: cssVars.primaryTealDark,
            light: cssVars.lightTeal,
            contrastText: cssVars.primaryWhite
        },
        secondary: {
            main: cssVars.secondaryTealDark,
            light: cssVars.secondaryBlueLight,
            dark: cssVars.primaryBlue,
            contrastText: cssVars.primaryWhite
        },
        background: {
            default: cssVars.backgroundPrimary,
            paper: cssVars.backgroundSecondary
        },
        text: {
            primary: cssVars.textPrimary,
            secondary: cssVars.textSecondary,
            disabled: cssVars.textDisabled
        },
        error: {
            main: cssVars.errorMain,
            dark: cssVars.errorDark,
            light: cssVars.errorLight
        },
        success: {
            main: cssVars.successMain,
            dark: cssVars.successDark,
            light: cssVars.successLight
        },
        warning: {
            main: cssVars.warningMain,
            dark: cssVars.warningDark,
            light: cssVars.warningLight
        },
        info: {
            main: cssVars.infoMain,
            dark: cssVars.infoDark,
            light: cssVars.infoLight
        },
        custom: {
            avatarBg: 'var(--avatar-bg)',
            avatarColor: 'var(--avatar-color)',
        },
        contractTypes: {
            license: { color: 'var(--primary-teal-dark)', bgColor: 'var(--background-accent-light)' },
            support: { color: 'var(--success-dark)', bgColor: 'var(--success-light)' },
            service: { color: 'var(--primary-blue)', bgColor: 'var(--info-light)' }
        },
        chart: {
            backgrounds: [
                cssVars.primaryTeal,
                cssVars.primaryBlue,
                cssVars.secondaryTealDark,
                cssVars.secondaryGrayLight,
            ]
        },
    },
    typography: {
        fontFamily: "var(--font-family-base, 'HCLTech Roobert', sans-serif)",
    },
    spacing: (factor) => `${0.25 * factor}rem`,
    shape: {
        borderRadius: 8,
    },
    shadows: [
        "none",
        "var(--shadow-xs, 0 1px 2px 0 rgba(0,0,0,0.05))",
        "var(--shadow-sm, 0 1px 3px 0 rgba(0,0,0,0.1))",
        "var(--shadow-md, 0 4px 6px -1px rgba(0,0,0,0.1))",
        "var(--shadow-lg, 0 10px 15px -3px rgba(0,0,0,0.1))",
        "var(--shadow-xl, 0 20px 25px -5px rgba(0,0,0,0.1))",
        "var(--shadow-2xl, 0 25px 50px -12px rgba(0,0,0,0.25))",
        "var(--shadow-inner, inset 0 2px 4px 0 rgba(0,0,0,0.05))",
        "none", "none", "none", "none", "none", "none", "none", "none", "none", "none", "none", "none", "none", "none", "none", "none", "none"
    ],
    components: {
        MuiButton: {
            styleOverrides: {
                root: {
                    borderRadius: 'var(--radius-md)',
                    textTransform: 'none',
                    fontWeight: 500,
                },
            },
        },
        MuiCard: {
            styleOverrides: {
                root: {
                    borderRadius: 'var(--radius-lg)',
                    boxShadow: 'var(--shadow-sm)',
                },
            },
        },
        MuiDrawer: {
            styleOverrides: {
                paper: {
                    borderLeft: 'none',
                    marginTop: '63px',
                    height: 'calc(100vh - 63px - 52px)',
                }
            }
        },
        MuiDialog: {
            styleOverrides: {
                paper: {
                    borderRadius: 'var(--radius-lg)',
                    maxWidth: '900px',
                }
            }
        },
        MuiDialogTitle: {
            styleOverrides: {
                root: {
                    backgroundColor: 'var(--primary-teal)',
                    color: 'var(--primary-white)',
                    padding: 'var(--space-md) var(--space-lg)',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                },
            }
        },
        MuiDialogContent: {
            styleOverrides: {
                root: {
                    padding: 0,
                    '& .MuiTabs-root': {
                        borderBottom: '1px solid var(--border-color)',
                        paddingLeft: 'var(--space-lg)',
                        paddingRight: 'var(--space-lg)',
                    },
                    '& > .MuiBox-root, & > .MuiTypography-root': {
                        padding: 'var(--space-md) var(--space-lg)',
                    }
                },
            }
        },
        MuiDialogActions: {
            styleOverrides: {
                root: {
                    padding: 'var(--space-sm) var(--space-lg)',
                    borderTop: '1px solid var(--border-color)',
                    backgroundColor: 'var(--background-primary)',
                },
            }
        },
        MuiTabs: {
            styleOverrides: {
                indicator: {
                    backgroundColor: 'var(--primary-teal)',
                    height: '3px',
                },
            }
        },
        MuiTab: {
            styleOverrides: {
                root: {
                    textTransform: 'none',
                    fontWeight: 600,
                    minWidth: 0,
                    padding: 'var(--space-sm) var(--space-md)',
                    '&.Mui-selected': {
                        color: 'var(--primary-teal)',
                    },
                },
            }
        },
    }
});

// --- STYLED COMPONENTS ---
export const AdminComponents = {
    // --- DIALOG & STEPPER STYLES ---
    // This component is now being replaced by ScrollableDialogContent to achieve the desired layout.
    // It's kept here to adhere to the "do not remove existing code" instruction.
    FixedHeightDialogContent: styled('div')(({ theme }) => ({
        height: '540px',
        overflowY: 'auto',
        padding: 0,
        background: 'var(--background-secondary)',
    })),
    // New component for the entire fixed header section of the dialog
    DialogHeaderContainer: styled(Box)(({ theme }) => ({
        backgroundColor: 'var(--background-secondary)',
        // paddingTop: theme.spacing(2),
    })),
    // New component to style the box containing the stepper
    StepperBox: styled(Box)(({ theme }) => ({
        paddingTop: theme.spacing(3),
        paddingLeft: theme.spacing(3),
        paddingRight: theme.spacing(3),
    })),
    // New container for the tabs
    TabsContainer: styled(Box)({}),
    // New DialogContent that handles the scrolling
    ScrollableDialogContent: styled(DialogContent)(({ theme }) => ({
        height: '540px',
        overflowY: 'auto',
        backgroundColor: 'var(--background-secondary)',
        padding: `0 !important`, // Use !important to override MuiDialogContent root styles
    })),
    // New component for the padding inside each TabPanel
    TabPanelContainer: styled(Box)(({ theme }) => ({
        padding: theme.spacing(3),
    })),
    // New component for the number inside the StepIcon
    StepIconText: styled('span')({
        fontWeight: 600,
    }),
    // New components for DialogTabView
    DialogTabViewControls: styled(Box)(({ theme }) => ({
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing(2),
        flexWrap: 'wrap',
        gap: theme.spacing(2),
    })),
    DialogTabViewActions: styled(Box)({}),
    DialogTabViewDeleteButton: styled(Button)(({ theme }) => ({
        marginLeft: theme.spacing(1),
    })),
    CustomStepConnector: styled(StepConnector)(({ theme }) => ({
        [`&.${stepConnectorClasses.alternativeLabel}`]: {
            top: 16,
        },
        [`& .${stepConnectorClasses.line}`]: {
            height: 3,
            border: 0,
            backgroundColor: 'var(--primary-teal)',
            borderRadius: 1,
        },
    })),
    StepIconRoot: styled(Box, {
        shouldForwardProp: (prop) => prop !== 'ownerState',
    })(({ theme, ownerState }) => ({
        color: ownerState.completed ? 'var(--success-main)' : 'var(--primary-teal)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: 32,
        height: 32,
        borderRadius: '50%',
        background: ownerState.completed ? 'var(--success-light)' : 'var(--primary-white)',
        border: ownerState.active ? '2px solid var(--primary-teal)' : '2px solid var(--border-color)',
        '& > .MuiSvgIcon-root': {
            fontSize: '1.2rem',
        },
    })),
    // --- END DIALOG/STEPPER STYLES ---

    // --- DIALOG FORM STYLES ---
    FormSection: styled(Box)(({ theme }) => ({
        marginBottom: theme.spacing(4),
        '&:last-of-type': {
            marginBottom: 0,
        },
    })),
    FormSectionTitle: styled(Typography)(({ theme }) => ({
        fontWeight: 'bold',
        color: 'var(--text-primary)',
        marginBottom: theme.spacing(3),
        paddingBottom: theme.spacing(1),
        borderBottom: `2px solid var(--primary-teal)`,
        display: 'inline-block',
    })),
    CenteredGridItem: styled(Grid)({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-start',
    }),
    FooterButtonContainer: styled(Box)({
        display: 'flex',
        alignItems: 'center',
    }),
    SpacedButton: styled(Button)(({ theme }) => ({
        marginLeft: theme.spacing(1),
    })),
    FooterSpacer: styled(Box)({
        flexGrow: 1,
    }),
    // --- END DIALOG FORM STYLES ---

    AppContainer: styled(Box)({
        display: 'flex',
        width: '100%',
        minHeight: '100vh',
        backgroundColor: 'var(--background-primary)',
    }),
    AppBody: styled(Box, { shouldForwardProp: (prop) => prop !== 'isSidebarOpen' })(({ isSidebarOpen }) => ({
        display: 'flex',
        flexDirection: 'column',
        flexGrow: 1,
        transition: 'margin-right var(--transition-duration-slow) var(--transition-easing-in-out)',
        overflow: 'hidden',
        marginRight: isSidebarOpen ? '350px' : '0',
    })),
    MainContentArea: styled('main', {
        shouldForwardProp: (prop) => prop !== 'isSidebarOpen'
    })(({ theme, isSidebarOpen }) => ({
        flexGrow: 1,
        padding: theme.spacing(4),
        transition: 'padding-right var(--transition-duration-slow) var(--transition-easing-in-out)',
        paddingRight: isSidebarOpen ? 0 : theme.spacing(4),
        display: 'flex',
        flexDirection: 'column',
        gap: theme.spacing(4),
        overflowY: 'auto',
    })),
    TopSectionWrapper: styled(Paper)(({ theme }) => ({
        padding: theme.spacing(4),
        borderRadius: 'var(--radius-lg)',
        flexShrink: 0,
    })),
    TopSectionContent: styled(Box)(({ theme }) => ({
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: theme.spacing(4),
    })),
    TopSectionActions: styled(Box)(({ theme }) => ({
        display: 'flex',
        gap: theme.spacing(2),
    })),
    SummaryCardsContainer: styled(Box)(({ theme }) => ({
        display: 'flex',
        gap: theme.spacing(4),
        flexWrap: 'wrap',
    })),
    SummaryCard: styled(Card, { shouldForwardProp: (prop) => prop !== 'isActive' })(({ theme, isActive }) => ({
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(3),
        padding: theme.spacing(4),
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        border: `1px solid ${isActive ? 'var(--primary-teal)' : 'var(--border-color)'}`,
        '&:hover': {
            borderColor: 'var(--primary-teal-dark)',
            transform: 'translateY(-4px)',
        },
    })),

    SummaryAvatar: styled(Avatar)(({ theme, variant }) => {
        // Map legacy or custom variants to root variables
        const variantMap = {
            total: 'total',
            active: 'active',
            inactive: 'inactive',
            warning: 'warning',
            error: 'error',
            success: 'success',
            info: 'info',
            expired: 'expired',
            paid: 'paid',
            unpaid: 'unpaid',
            'partially-paid': 'partially-paid',
            approved: 'approved',
            pending: 'pending',
            secondary: 'secondary',
            primary: 'primary',
        };
        const cssVar = variantMap[variant] ? `--${variantMap[variant]}-main` : undefined;
        return {
            backgroundColor: cssVar ? `var(${cssVar}, ${theme.palette.secondary.main})` : theme.palette.secondary.main,
        };
    }),
    SummaryTrend: styled(Typography)(({ theme, color }) => ({
        color: `var(--${color}-main, ${theme.palette.text.secondary})`,
    })),
    ControlsSection: styled(Paper)(({ theme }) => ({
        padding: theme.spacing(4),
        borderRadius: 'var(--radius-lg)',
        display: 'flex',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: theme.spacing(4),
        flexShrink: 0,
    })),
    ControlsGroup: styled(Box)(({ theme }) => ({
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(4),
        flexWrap: 'wrap',
    })),
    ContentBody: styled(Box)(({ theme }) => ({
        display: 'flex',
        gap: theme.spacing(4),
        flexGrow: 1,
        minHeight: 0,
        alignItems: 'flex-start',
    })),
    MainLeftPane: styled(Paper)(({ theme }) => ({
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        minWidth: 0,
        height: '65vh',
        padding: theme.spacing(4),
        borderRadius: 'var(--radius-lg)',
    })),
    DetailsPane: styled(Paper, { shouldForwardProp: (prop) => prop !== 'isCollapsed' })(({ isCollapsed, theme }) => ({
        flexBasis: isCollapsed ? 0 : '30%',
        flexShrink: 0,
        display: 'flex',
        flexDirection: 'column',
        minWidth: isCollapsed ? 0 : '300px',
        transition: 'all var(--transition-duration-base) var(--transition-easing-in-out)',
        overflow: 'hidden',
        height: '65vh',
        padding: isCollapsed ? 0 : theme.spacing(4),
        borderRadius: 'var(--radius-lg)',
        visibility: isCollapsed ? 'hidden' : 'visible',
    })),
    ViewContainer: styled(Box)({
        flexGrow: 1,
        minHeight: 0,
        overflowY: 'auto',
    }),
    GridView: styled(Box)(({ theme }) => ({
        padding: '4px',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
        gap: theme.spacing(4),
        alignContent: 'flex-start',
    })),
    CompactView: styled(Box)(({ theme }) => ({
        padding: '4px',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(260px, 1fr))',
        gap: theme.spacing(4),
        alignContent: 'flex-start',
    })),
    ListView: styled(Box)(({ theme }) => ({
        padding: '4px',
        display: 'flex',
        flexDirection: 'column',
        gap: theme.spacing(2),
    })),
    TableViewContainer: styled(MuiTableContainer)({
        flexGrow: 1,
        overflow: 'auto'
    }),
    ResponsiveTable: styled(Table)({
        tableLayout: 'fixed',
        width: '100%',
    }),
    StatusBadge: styled(Chip)(({ theme, ownerState }) => {
        let color = theme.palette.text.primary;
        let backgroundColor = 'var(--secondary-gray-light)';
        if (ownerState?.status) {
            if (['Active', 'Online'].includes(ownerState.status)) {
                color = theme.palette.success.dark;
                backgroundColor = theme.palette.success.light;
            } else if (['Inactive', 'Expired', 'Offline'].includes(ownerState.status)) {
                color = theme.palette.error.dark;
                backgroundColor = theme.palette.error.light;
            } else if (['Expiring', 'Maintenance'].includes(ownerState.status)) {
                color = theme.palette.warning.dark;
                backgroundColor = theme.palette.warning.light;
            }
        } else if (ownerState?.color && ownerState?.bgColor) {
            color = ownerState.color;
            backgroundColor = ownerState.bgColor;
        }
        return { color, backgroundColor, fontWeight: 600 };
    }),
    CardBase: styled(Card, { shouldForwardProp: (prop) => prop !== 'isSelected' })(({ theme, isSelected }) => ({
        position: 'relative',
        cursor: 'pointer',
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.2s ease',
        border: `1px solid ${isSelected ? 'var(--primary-teal)' : 'var(--border-color)'}`,
        '&:hover': {
            borderColor: 'var(--primary-teal-dark)',
            transform: 'translateY(-4px)',
        },
        '& .card-checkbox': {
            position: 'absolute',
            top: theme.spacing(2),
            left: theme.spacing(2),
            zIndex: 10,
        },
    })),
    CardActionContainer: styled(Box)(({ theme }) => ({
        position: 'absolute',
        top: theme.spacing(2),
        right: theme.spacing(2),
        zIndex: 10,
    })),
    PaddedCardContent: styled(CardContent)(({ theme }) => ({
        paddingTop: theme.spacing(10),
    })),
    IndustryTypography: styled(Typography)(({ theme }) => ({
        marginBottom: theme.spacing(3),
    })),
    ContactTypography: styled(Typography)(({ theme }) => ({
        marginTop: theme.spacing(2),
    })),
    CardDivider: styled(Divider)(({ theme }) => ({
        margin: `${theme.spacing(3)} 0`,
    })),
    CardDetailRow: styled(Typography)({
        display: 'flex',
        justifyContent: 'space-between',
    }),
    CompactCardContent: styled(CardContent)(({ theme }) => ({
        paddingTop: theme.spacing(10),
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        flexGrow: 1,
    })),
    CompactCardFooter: styled(Box)(({ theme }) => ({
        marginTop: theme.spacing(2),
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
    })),
    ListItemCard: styled(Card, { shouldForwardProp: (prop) => prop !== 'isSelected' })(({ theme, isSelected }) => ({
        padding: theme.spacing(2),
        marginBottom: theme.spacing(2),
        position: 'relative',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        border: `1px solid ${isSelected ? 'var(--primary-teal)' : 'var(--border-color)'}`,
        '&:hover': {
            borderColor: 'var(--primary-teal)',
            transform: 'translateY(-4px)',
        },
    })),
    ListItemGrid: styled(Box)(({ theme }) => ({
        display: 'grid',
        gridTemplateColumns: '50px 3fr 2fr 2fr auto auto',
        gap: theme.spacing(4),
        alignItems: 'center',
    })),
    ListItemActions: styled(Box)({
        justifySelf: 'end',
    }),
    ClickableTypography: styled(Typography)({
        cursor: 'pointer',
        fontWeight: 'bold',
        '&:hover': {
            color: 'var(--primary-teal)',
        },
    }),
    DraggableHeaderCell: styled(TableCell)({
        cursor: 'move',
    }),
    ContentTableCell: styled(TableCell)({
        whiteSpace: 'normal',
        wordWrap: 'break-word',
    }),
    GroupHeaderRow: styled(TableRow)({
        '& > *': {
            borderBottom: 'unset',
        },
    }),
    GroupHeaderCell: styled(TableCell, { shouldForwardProp: (prop) => prop !== 'level' })(({ theme, level = 0 }) => ({
        fontWeight: 'bold',
        backgroundColor: 'var(--secondary-gray-light)',
        whiteSpace: 'normal',
        wordWrap: 'break-word',
        paddingLeft: theme.spacing(4 + level * 4),
    })),
    ActionTableCell: styled(TableCell)({
        whiteSpace: 'nowrap',
    }),
    GraphContainer: styled(Box)(({ theme }) => ({
        position: 'relative',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: theme.spacing(4),
    })),
    GraphCanvasContainer: styled(Box)({
        flexGrow: 1,
        position: 'relative'
    }),
    CenteredMessage: styled(Box)({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        flexDirection: 'column',
        textAlign: 'center',
    }),
    LargeIcon: styled(SearchOff)({
        fontSize: '4rem',
    }),
    ActivityLogPaper: styled(Paper)(({ theme }) => ({
        padding: theme.spacing(4),
        borderRadius: 'var(--radius-lg)',
        flexShrink: 0,
    })),
    ActivityLogTitle: styled(Typography)(({ theme }) => ({
        fontWeight: 'bold',
        color: 'var(--text-primary)',
        marginBottom: theme.spacing(4),
    })),
    ActivityLogListContainer: styled(Box)(({ theme }) => ({
        maxHeight: '160px',
        overflowY: 'auto',
        paddingRight: theme.spacing(2),
    })),
    ActivityLogListItem: styled(ListItem)(({ theme }) => ({
        paddingTop: theme.spacing(3),
        paddingBottom: theme.spacing(3),
        borderBottom: `1px solid var(--border-color)`,
        '&:last-child': {
            borderBottom: 'none',
        },
    })),
    ActivityLogIconContainer: styled(ListItemIcon)({
        minWidth: 40,
    }),
    ActivityLogAvatar: styled('div')({
        backgroundColor: 'var(--avatar-bg)',
        width: 32,
        height: 32,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    }),
    ActivityLogTextContainer: styled(Box)({
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
    }),
    ActivityLogLink: styled('a')({
        color: 'var(--primary-teal)',
        textDecoration: 'none',
        fontWeight: 500,
    }),
    StyledToggleButtonGroup: styled(ToggleButtonGroup)(({ theme }) => ({
        border: `1px solid var(--border-color)`,
        borderRadius: 'var(--radius-md)',
        overflow: 'hidden',
        '& .MuiToggleButton-root': {
            textTransform: 'capitalize',
            border: 'none',
            color: 'var(--text-secondary)',
            padding: `${theme.spacing(1)} ${theme.spacing(2)}`,
            '&:not(:first-of-type)': {
                borderLeft: `1px solid var(--border-color)`,
            },
            '&.Mui-selected': {
                backgroundColor: 'var(--secondary-teal-dark)',
                color: 'var(--primary-white)',
                '&:hover': {
                    backgroundColor: 'var(--primary-teal)',
                },
            },
            '&.Mui-selected .MuiSvgIcon-root': {
                color: 'var(--primary-white)',
            },
            '& .MuiSvgIcon-root': {
                marginRight: theme.spacing(2),
            },
        }
    })),
    SidebarContainer: styled(Box)({
        width: 360,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: 'var(--background-secondary)',
        borderLeft: `1px solid var(--border-color)`,
    }),
    SidebarHeader: styled(Box)(({ theme }) => ({
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: `${theme.spacing(2)} ${theme.spacing(4)}`,
        borderBottom: `1px solid var(--border-color)`,
        flexShrink: 0,
    })),
    SidebarContent: styled(Box)(({ theme }) => ({
        padding: theme.spacing(4),
        overflowY: 'auto',
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        gap: theme.spacing(6),
    })),
    SidebarFooter: styled(Box)(({ theme }) => ({
        padding: theme.spacing(4),
        borderTop: `1px solid var(--border-color)`,
        display: 'flex',
        gap: theme.spacing(2),
        justifyContent: 'flex-end',
        flexShrink: 0,
    })),
    SidebarSection: styled(Box)(({ theme }) => ({
        display: 'flex',
        flexDirection: 'column',
        gap: theme.spacing(3),
    })),
    SidebarSectionTitle: styled(Typography)(({ theme }) => ({
        fontWeight: 'bold',
        color: 'var(--text-primary)',
    })),
    QuickFilterContainer: styled(Box)(({ theme }) => ({
        display: 'flex',
        flexWrap: 'wrap',
        gap: theme.spacing(2),
    })),
    FilterChipContainer: styled(Box)(({ theme }) => ({
        display: 'flex',
        flexWrap: 'wrap',
        gap: theme.spacing(2),
        minHeight: '24px',
    })),
    ColumnVisibilityContainer: styled(Box)(({ theme }) => ({
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: `${theme.spacing(0)} ${theme.spacing(4)}`,
        maxHeight: '250px',
        overflowY: 'auto',
    })),
    ActionMenuItem: styled(MenuItem)(({ theme }) => ({
        gap: theme.spacing(3),
    })),
    ErrorMenuItem: styled(MenuItem)(({ theme }) => ({
        color: 'var(--error-main)',
        gap: theme.spacing(3),
    })),
    ColumnActionContainer: styled(Box)(({ theme }) => ({
        display: 'flex',
        gap: theme.spacing(2),
        marginBottom: theme.spacing(2),
    })),
    ChartTypeSelector: styled(ToggleButtonGroup)(({ theme }) => ({
        alignSelf: 'center',
    })),
    EmptyStateIcon: styled(Description)(({ theme }) => ({
        fontSize: '4rem',
        marginBottom: theme.spacing(4),
        color: 'var(--text-disabled)',
    })),
    ChartTypeSelectorContainer: styled(Box)(({ theme }) => ({
        marginBottom: theme.spacing(4),
    })),
    DraggableListItemPaper: styled(Paper)({
        cursor: 'grab',
        '&:hover': {
            backgroundColor: 'var(--background-hover-light)',
        },
        borderRadius: 'var(--radius-md)',
        userSelect: 'none',
    }),
    DragIndicatorIcon: styled(ListItemIcon)(({ theme }) => ({
        minWidth: 'auto',
        marginRight: theme.spacing(2),
        color: 'var(--text-secondary)',
    })),
    ListItemIconStyled: styled(ListItemIcon)(({ theme }) => ({
        minWidth: 'auto',
        marginRight: theme.spacing(4),
    })),
    ConfigureMenuModalPaper: styled(Paper)(({ theme }) => ({
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 800,
        maxWidth: '90vw',
        padding: theme.spacing(6),
        borderRadius: 'var(--radius-lg)',
    })),
    ConfigureMenuModalTitle: styled(Typography)(({ theme }) => ({
        marginBottom: theme.spacing(2),
    })),
    ConfigureMenuModalSubtitle: styled(Typography)(({ theme }) => ({
        marginBottom: theme.spacing(6),
    })),
    ConfigureMenuModalListTitle: styled(Typography)(({ theme }) => ({
        marginBottom: theme.spacing(2),
        paddingLeft: theme.spacing(2),
    })),
    ConfigureMenuModalListPaper: styled(Paper)({
        padding: 'var(--space-sm)',
        minHeight: 300,
        maxHeight: '60vh',
        overflowY: 'auto',
        backgroundColor: 'var(--background-hover-light)',
    }),
    ConfigureMenuModalActions: styled(Box)(({ theme }) => ({
        marginTop: theme.spacing(6),
        display: 'flex',
        justifyContent: 'flex-end',
        gap: theme.spacing(2),
    })),
    AdminNavSidebar: styled(Box)({
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
    }),
    AdminNavSidebarSearch: styled(Box)(({ theme }) => ({
        padding: theme.spacing(2),
        borderBottom: `1px solid var(--border-color)`,
        flexShrink: 0,
    })),
    AdminNavSidebarNav: styled(Box, {
        shouldForwardProp: (prop) => prop !== 'isCollapsed'
    })(({ isCollapsed, theme }) => ({
        flexGrow: 1,
        overflowY: isCollapsed ? 'hidden' : 'auto',
        overflowX: 'visible',
        padding: theme.spacing(2),
    })),
    AdminNavTopBar: styled(Box)({
        padding: '0 1rem',
        backgroundColor: 'var(--background-secondary)',
        borderBottom: '1px solid var(--border-color)',
    }),
    AdminNavScrollContainer: styled(Box)({
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        position: 'relative',
        minWidth: 0,
    }),
    AdminNavActionsContainer: styled(Box)({}),
    AdminNavViewport: styled(Box)({
        flex: 1,
        overflow: 'hidden',
        minWidth: 0,
    }),
    AdminNavTopBarList: styled('ul')({
        display: 'flex',
        listStyle: 'none',
        margin: 0,
        padding: 0,
        gap: '1rem',
        justifyContent: 'flex-start',
        alignItems: 'center',
        flexWrap: 'nowrap',
    }),
    MegaMenuDivider: styled(Divider)(({ theme }) => ({
        margin: `${theme.spacing(0)} ${theme.spacing(2)}`,
    })),
    AdminNavTopBarMoreButton: styled(Button)({
        color: 'var(--text-primary)',
        textTransform: 'none',
        padding: '1rem 0.75rem',
        fontWeight: 'normal',
    }),
    AdminNavMegaMenu: styled(Popover)(({ theme }) => ({
        '& .MuiPaper-root': {
            minWidth: 500,
            maxWidth: '90vw',
            padding: theme.spacing(2),
            borderRadius: 'var(--radius-lg)',
            boxShadow: 'var(--shadow-lg)',
            marginTop: theme.spacing(2),
        },
    })),
    MegaMenuContent: styled(Box)(({ theme }) => ({
        display: 'flex',
        gap: theme.spacing(4),
        padding: theme.spacing(4),
    })),
    MegaMenuCategory: styled(Box)({
        flex: 1,
    }),
    MegaMenuListSubheader: styled(ListSubheader)({
        fontWeight: 'bold',
        color: 'var(--text-primary)',
        lineHeight: '2.5rem',
    }),
    MegaMenuItem: styled(MenuItem)(({ theme }) => ({
        borderRadius: 'var(--radius-md)',
        padding: theme.spacing(3),
        '&.active': {
            backgroundColor: 'var(--background-accent-light)',
            fontWeight: 'bold',
        },
    })),
    MegaMenuItemIcon: styled(ListItemIcon)({
        minWidth: 36,
    }),
    MegaMenuConfigureItem: styled(MenuItem)(({ theme }) => ({
        margin: theme.spacing(2),
        borderRadius: 'var(--radius-md)',
    })),
    MessageContainer: styled('div')(({ messageType }) => ({
        position: 'fixed',
        top: 80,
        right: 32,
        background: messageType === 'success' ? '#2ecc40' : 'var(--primary-teal)',
        color: '#fff',
        padding: '12px 28px',
        borderRadius: 10,
        zIndex: 3000,
        fontWeight: 500,
        fontSize: '1.08rem',
        boxShadow: '0 4px 18px rgba(0,128,198,0.18)',
        minWidth: 220,
        textAlign: 'center',
        letterSpacing: 0.1,
        transition: 'opacity 0.3s',
        display: 'flex',
        alignItems: 'center',
        gap: 10,
    })),
    MessageIcon: styled('span')({
        fontSize: '1.3em',
        marginRight: 8,
    }),
    AdminLayoutContainer: styled(Box)({
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
    }),
    AdminMainBody: styled(Box)({
        display: 'flex',
        flex: 1,
        overflow: 'hidden',
    }),
    AdminContentContainer: styled(Box)({
        display: 'flex',
        flexDirection: 'column',
        flex: 1,
        overflow: 'hidden',
    }),
    AdminMainContent: styled(Box)(({ theme }) => ({
        flex: 1,
        padding: theme.spacing(4),
        overflowY: 'auto',
    })),
    TopBarSearch: styled('div')(({ theme }) => ({
        position: 'relative',
        borderRadius: 'var(--radius-md)',
        backgroundColor: 'rgba(0, 0, 0, 0.05)',
        '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.08)',
        },
        width: '100%',
        maxWidth: '550px',
    })),
    TopBarSearchIconWrapper: styled('div')(({ theme }) => ({
        padding: `${theme.spacing(0)} ${theme.spacing(4)}`,
        height: '100%',
        position: 'absolute',
        pointerEvents: 'none',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'var(--text-secondary)',
    })),
    TopBarStyledInputBase: styled(InputBase)(({ theme }) => ({
        color: 'inherit',
        width: '100%',
        '& .MuiInputBase-input': {
            padding: `${theme.spacing(2.5)} ${theme.spacing(2.5)} ${theme.spacing(2.5)} 0`,
            paddingLeft: `calc(1em + ${theme.spacing(8)})`,
            transition: theme.transitions.create('width'),
        },
    })),
    DraggableListItemStyled: styled(ListItem)({
        cursor: 'grab',
        '&:hover': {
            backgroundColor: 'var(--background-hover-light)',
        },
        borderRadius: 'var(--radius-md)',
        userSelect: 'none',
    }),
    DraggableListItemIcon: styled(ListItemIcon)(({ theme }) => ({
        minWidth: 'auto',
        marginRight: theme.spacing(2),
        color: 'var(--text-secondary)',
    })),
    ConfigureTopBarModalPaper: styled(Paper)(({ theme }) => ({
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 800,
        maxWidth: '90vw',
        padding: theme.spacing(6),
        borderRadius: 'var(--radius-lg)',
    })),
    ConfigureTopBarModalTitle: styled(Typography)(({ theme }) => ({
        marginBottom: theme.spacing(2),
    })),
    ConfigureTopBarModalSubtitle: styled(Typography)(({ theme }) => ({
        marginBottom: theme.spacing(6),
    })),
    ConfigureTopBarModalListTitle: styled(Typography)(({ theme }) => ({
        marginBottom: theme.spacing(2),
        paddingLeft: theme.spacing(2),
    })),
    ConfigureTopBarModalListPaper: styled(Paper)({
        padding: 'var(--space-sm)',
        minHeight: 300,
        maxHeight: '60vh',
        overflowY: 'auto',
        backgroundColor: 'var(--background-hover-light)',
    }),
    ConfigureTopBarModalActions: styled(Box)(({ theme }) => ({
        marginTop: theme.spacing(6),
        display: 'flex',
        justifyContent: 'flex-end',
        gap: theme.spacing(2),
    })),
    TopBarAppBar: styled(AppBar)({
        background: 'var(--primary-white) !important',
        color: 'var(--topbar-icon) !important',
        borderBottom: '1px solid var(--border-color)',
        zIndex: 100,
        width: '100%',
        transition: 'width 0.3s, margin-left 0.3s',
        height: '64px',
        justifyContent: 'center',
        position: 'sticky',
        top: 0,
        left: 0,
    }),
    TopBarToolbar: styled(Toolbar)({
        justifyContent: 'space-between',
    }),
    TopBarLeftSection: styled(Box)({
        flex: 1,
    }),
    TopBarCenterSection: styled(Box)({
        flex: 1.5,
        display: 'flex',
        justifyContent: 'center',
    }),
    TopBarRightSection: styled(Box)({
        flex: 1,
        display: 'flex',
        justifyContent: 'flex-end',
        alignItems: 'center',
        gap: '0.5rem',
    }),
    TopBarUserButton: styled(Button)({
        textTransform: 'none',
        borderRadius: 'var(--radius-xxl)',
    }),
    TopBarUserAvatar: styled(Avatar)(({ theme }) => ({
        width: 32,
        height: 32,
        marginRight: theme.spacing(2),
    })),
    TopBarAppsMenu: styled(Popover, {
        shouldForwardProp: (prop) => prop !== 'popoverView',
    })(({ popoverView, theme }) => ({
        '& .MuiPaper-root': {
            padding: popoverView === 'main' ? theme.spacing(2) : 0,
            minWidth: 320,
            maxWidth: '90vw',
            borderRadius: 'var(--radius-lg)',
            border: '1px solid',
            borderColor: 'divider',
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
            marginTop: 1.5,
            '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
                borderTop: '1px solid',
                borderLeft: '1px solid',
                borderColor: 'divider',
            },
        },
    })),
    PopoverHeaderBox: styled(Box)(({ theme }) => ({
        display: 'flex',
        alignItems: 'center',
        padding: theme.spacing(2),
        borderBottom: '1px solid',
        borderColor: 'divider',
    })),
    PopoverHeaderTitle: styled(Typography)({
        fontWeight: 'bold',
    }),
    NotificationListItem: styled(ListItem)(({ theme }) => ({
        '& .unread-indicator': {
            width: 8,
            height: 8,
            borderRadius: '50%',
            backgroundColor: 'var(--primary-teal)',
            marginLeft: theme.spacing(2),
        },
    })),
    ViewAllButtonContainer: styled(Box)(({ theme }) => ({
        padding: theme.spacing(2),
        textAlign: 'center',
    })),
    DirectMenuPopover: styled(Popover)(({ theme }) => ({
        '& .MuiPaper-root': {
            minWidth: 280,
            borderRadius: 'var(--radius-lg)',
            border: '1px solid',
            borderColor: 'divider',
            marginTop: 1.5,
        },
    })),
    FooterContainer: styled(Box)({
        padding: '1rem',
        textAlign: 'center',
        flexShrink: 0,
        backgroundColor: 'var(--primary-blue)',
        color: '#ffffff',
    }),
    FooterLink: styled(Link)(({ theme }) => ({
        marginLeft: theme.spacing(4),
        color: '#ffffff',
        textDecoration: 'none',
        opacity: 0.8,
        '&:hover': {
            opacity: 1,
        },
    })),
    BreadcrumbsContainer: styled(Box)({
        padding: 0,
        marginBottom: '1.5rem',
    }),
    BreadcrumbsLink: styled(Link)({
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
    }),
    VerticalDivider: styled(Divider)(({ theme }) => ({
        margin: `${theme.spacing(0)} ${theme.spacing(2)}`,
    })),
    StyledDivider: styled(Divider)(({ theme }) => ({
        margin: `${theme.spacing(2)} 0`,
    })),
    AppsGridMenuItem: styled(MenuItem)(({ theme }) => ({
        flexDirection: 'column',
        height: 80,
        borderRadius: 'var(--radius-md)',
        gap: '0.5rem',
        '&:hover': {
            backgroundColor: 'var(--background-hover-light)',
        },
    })),
    NotificationListStyled: styled(List)({
        padding: 0,
    }),
    StyledScheduleIcon: styled(Schedule)({
        color: 'var(--avatar-color)',
    }),
    FormContainer: styled('form')(({ theme }) => ({
        display: 'flex',
        flexDirection: 'column',
        gap: theme.spacing(4),
        marginTop: theme.spacing(4)
    })),
    GraphWrapper: styled(Box)(({ theme }) => ({
        width: '100%',
        height: '100%',
        padding: theme.spacing(2),
        display: 'flex',
        flexDirection: 'column'
    })),
    WrappedFormControlLabel: styled(FormControlLabel)(({ theme }) => ({
        alignItems: 'flex-start',
        '& .MuiFormControlLabel-label': {
            whiteSpace: 'normal',
            wordBreak: 'break-word',
            paddingTop: '9px',
            paddingBottom: '9px',
        },
    })),
    ModalAlert: styled(Alert)(({ theme }) => ({
        marginBottom: theme.spacing(4),
    })),
    CardDetailsGrid: styled(Box)(({ theme }) => ({
        display: 'grid',
        gridTemplateColumns: 'auto 1fr',
        gap: theme.spacing(1, 4),
        alignItems: 'center',
    })),
    CardDetailLabel: styled(Typography)(({ theme }) => ({
        fontWeight: 'bold',
        color: theme.palette.text.primary,
    })),

    // --- DIALOG COMPONENTS ---
    DialogHeader: styled(DialogTitle)(({ theme }) => ({
        backgroundColor: 'var(--primary-teal)',
        color: 'var(--primary-white)',
        padding: `${theme.spacing(2)} ${theme.spacing(4)}`,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        '& .MuiButton-root': {
            color: 'var(--primary-white)',
            borderColor: 'rgba(255, 255, 255, 0.5)',
        },
        '& .MuiTypography-root': {
            color: 'var(--primary-white)',
        }
    })),
    DialogContent: styled(DialogContent)(({ theme }) => ({
        padding: theme.spacing(4),
        backgroundColor: 'var(--background-secondary)',
    })),
    DialogActions: styled(DialogActions)(({ theme }) => ({
        padding: `${theme.spacing(2)} ${theme.spacing(4)}`,
        borderTop: `1px solid var(--border-color)`,
        backgroundColor: 'var(--background-primary)',
        display: 'flex',
        justifyContent: 'flex-end',
        gap: theme.spacing(2),
    })),
    FormGrid: styled(Grid)({
        // No specific styles needed here, using Grid's props directly
    }),
    FormLabel: styled(Typography)(({ theme }) => ({
        fontWeight: 'normal',
        color: theme.palette.text.primary,
        textAlign: 'right',
        paddingTop: theme.spacing(2), // Align with textfield center
        [theme.breakpoints.down('sm')]: {
            textAlign: 'left',
            fontWeight: 'bold',
            paddingTop: 0,
        },
    })),
    DialogProgressContainer: styled(Box)({
        flexGrow: 1,
    }),
    DialogProgressBar: styled(LinearProgress)(({ theme }) => ({
        height: 6,
        borderRadius: 3,
        marginTop: theme.spacing(2),
        '& .MuiLinearProgress-bar': {
            backgroundColor: 'var(--primary-white)',
        },
        backgroundColor: 'rgba(255, 255, 255, 0.3)',
    })),
    DialogFooter: styled(DialogActions)(({ theme }) => ({
        padding: `${theme.spacing(2)} ${theme.spacing(4)}`,
        borderTop: `1px solid var(--border-color)`,
        backgroundColor: 'var(--background-primary)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: theme.spacing(2),
    })),
    DialogSummary: styled(Box)(({ theme }) => ({
        display: 'flex',
        gap: theme.spacing(4),
        alignItems: 'center',
        color: theme.palette.text.secondary,
    })),
    StyledTabs: styled(Tabs)({
        borderBottom: `1px solid var(--border-color)`,
        '& .MuiTabs-indicator': {
            backgroundColor: 'var(--primary-teal)',
        },
    }),
    StyledTab: styled(Tab)({
        textTransform: 'none',
        fontWeight: 'bold',
        '&.Mui-selected': {
            color: 'var(--primary-teal)',
        },
    }),

};
export default theme;