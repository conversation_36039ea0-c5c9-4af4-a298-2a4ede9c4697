import React, { useState, useMemo, useEffect } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab, Grid, Dialog, DialogContent, DialogActions, Stepper, Step, StepLabel
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, CheckCircle, Cancel, People, PieChart, DonutLarge, ShowChart, Settings, Save, Close
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import { calculateDerivedValues, emptyPurchaseOrder, fieldDefinitions, dropdownOptions, validatePurchaseOrder } from '../../../models/PurchaseOrderModel';
import Chart from 'chart.js/auto';
// Remove import of PurchaseOrderCharts

// --- MOCK DATA (from PurchaseOrderManagement.jsx) ---
const initialPurchaseOrders = [
  {
    id: 'po-001',
    owner: 'John Smith',
    parentCompanyName: 'Acme Corporation',
    accountName: 'Acme Global Services',
    domainVertical: 'Manufacturing',
    region: 'North America',
    revenueType: 'License',
    poNumber: 'PO-2025-001',
    poCurrency: 'USD',
    poValue: 100000,
    conversionRate: 83.5,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Closed Won',
    frequencyOfRealization: 'Monthly',
    startDate: '2025-04-01',
    endDate: '2026-03-31',
    renewalUpsell: 'New',
    productRevenueCategory: 'Software',
    productName: 'HCL Commerce Suite',
    projectDescription: 'Enterprise e-commerce platform implementation',
    status: 'Approved',
    createdBy: 'Jane Doe',
    createdDate: '2024-03-15T10:30:00Z',
    lastModifiedBy: 'Jane Doe',
    lastModifiedDate: '2024-03-15T10:30:00Z',
    projectedApr25: 8333.33,
    projectedMay25: 8333.33,
    projectedJun25: 8333.33,
    projectedJul25: 8333.33,
    projectedAug25: 8333.33,
    projectedSep25: 8333.33,
    projectedOct25: 8333.33,
    projectedNov25: 8333.33,
    projectedDec25: 8333.33,
    projectedJan26: 8333.33,
    projectedFeb26: 8333.33,
    projectedMar26: 8333.37,
    actualApr25: 8333.33,
    actualMay25: 8333.33,
    actualJun25: 0,
    actualJul25: 0,
    actualAug25: 0,
    actualSep25: 0,
    actualOct25: 0,
    actualNov25: 0,
    actualDec25: 0,
    actualJan26: 0,
    actualFeb26: 0,
    actualMar26: 0
  },
  {
    id: 'po-002',
    owner: 'Sarah Johnson',
    parentCompanyName: 'TechGlobal Inc.',
    accountName: 'TechGlobal Solutions',
    domainVertical: 'Technology',
    region: 'Europe',
    revenueType: 'Services',
    poNumber: 'PO-2025-002',
    poCurrency: 'EUR',
    poValue: 75000,
    conversionRate: 90.2,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Closed Won',
    frequencyOfRealization: 'Quarterly',
    startDate: '2025-04-01',
    endDate: '2026-03-31',
    renewalUpsell: 'New',
    productRevenueCategory: 'Professional Services',
    productName: 'HCL Digital Experience',
    projectDescription: 'Digital transformation consulting services',
    status: 'Pending',
    createdBy: 'Mike Wilson',
    createdDate: '2024-03-10T14:45:00Z',
    lastModifiedBy: 'Mike Wilson',
    lastModifiedDate: '2024-03-10T14:45:00Z',
    projectedApr25: 16875,
    projectedMay25: 16875,
    projectedJun25: 16875,
    projectedJul25: 16875,
    projectedAug25: 16875,
    projectedSep25: 16875,
    projectedOct25: 16875,
    projectedNov25: 16875,
    projectedDec25: 16875,
    projectedJan26: 16875,
    projectedFeb26: 16875,
    projectedMar26: 16875,
    actualApr25: 16875,
    actualMay25: 0,
    actualJun25: 0,
    actualJul25: 0,
    actualAug25: 0,
    actualSep25: 0,
    actualOct25: 0,
    actualNov25: 0,
    actualDec25: 0,
    actualJan26: 0,
    actualFeb26: 0,
    actualMar26: 0
  },
  {
    id: 'po-003',
    owner: 'Alex Turner',
    parentCompanyName: 'Global Manufacturing Ltd.',
    accountName: 'Global Manufacturing Services',
    domainVertical: 'Manufacturing',
    region: 'Asia',
    revenueType: 'License',
    poNumber: 'PO-2025-003',
    poCurrency: 'INR',
    poValue: 120000,
    conversionRate: 1,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Negotiation',
    frequencyOfRealization: 'Monthly',
    startDate: '2025-05-01',
    endDate: '2026-04-30',
    renewalUpsell: 'Renewal',
    productRevenueCategory: 'Software',
    productName: 'HCL Manufacturing Suite',
    projectDescription: 'Manufacturing automation software',
    status: 'Approved',
    createdBy: 'Alex Turner',
    createdDate: '2024-04-10T09:00:00Z',
    lastModifiedBy: 'Alex Turner',
    lastModifiedDate: '2024-04-10T09:00:00Z',
    projectedApr25: 10000,
    projectedMay25: 10000,
    projectedJun25: 10000,
    projectedJul25: 10000,
    projectedAug25: 10000,
    projectedSep25: 10000,
    projectedOct25: 10000,
    projectedNov25: 10000,
    projectedDec25: 10000,
    projectedJan26: 10000,
    projectedFeb26: 10000,
    projectedMar26: 10000,
    actualApr25: 10000,
    actualMay25: 0,
    actualJun25: 0,
    actualJul25: 0,
    actualAug25: 0,
    actualSep25: 0,
    actualOct25: 0,
    actualNov25: 0,
    actualDec25: 0,
    actualJan26: 0,
    actualFeb26: 0,
    actualMar26: 0
  },
  {
    id: 'po-004',
    owner: 'Priya Singh',
    parentCompanyName: 'AgroTech Pvt Ltd.',
    accountName: 'AgroTech Solutions',
    domainVertical: 'Agriculture',
    region: 'Africa',
    revenueType: 'Services',
    poNumber: 'PO-2025-004',
    poCurrency: 'USD',
    poValue: 95000,
    conversionRate: 83.5,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Closed Lost',
    frequencyOfRealization: 'Quarterly',
    startDate: '2025-06-01',
    endDate: '2026-05-31',
    renewalUpsell: 'Upsell',
    productRevenueCategory: 'Professional Services',
    productName: 'HCL Agro Suite',
    projectDescription: 'Agriculture management services',
    status: 'Pending',
    createdBy: 'Priya Singh',
    createdDate: '2024-05-12T11:15:00Z',
    lastModifiedBy: 'Priya Singh',
    lastModifiedDate: '2024-05-12T11:15:00Z',
    projectedApr25: 23750,
    projectedMay25: 23750,
    projectedJun25: 23750,
    projectedJul25: 23750,
    projectedAug25: 23750,
    projectedSep25: 23750,
    projectedOct25: 23750,
    projectedNov25: 23750,
    projectedDec25: 23750,
    projectedJan26: 23750,
    projectedFeb26: 23750,
    projectedMar26: 23750,
    actualApr25: 0,
    actualMay25: 0,
    actualJun25: 0,
    actualJul25: 0,
    actualAug25: 0,
    actualSep25: 0,
    actualOct25: 0,
    actualNov25: 0,
    actualDec25: 0,
    actualJan26: 0,
    actualFeb26: 0,
    actualMar26: 0
  },
  {
    id: 'po-005',
    owner: 'Maria Lopez',
    parentCompanyName: 'Energy Solutions Inc.',
    accountName: 'Energy Solutions',
    domainVertical: 'Energy',
    region: 'Europe',
    revenueType: 'License',
    poNumber: 'PO-2025-005',
    poCurrency: 'EUR',
    poValue: 110000,
    conversionRate: 90.2,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Closed Won',
    frequencyOfRealization: 'Monthly',
    startDate: '2025-07-01',
    endDate: '2026-06-30',
    renewalUpsell: 'New',
    productRevenueCategory: 'Software',
    productName: 'HCL Energy Suite',
    projectDescription: 'Energy analytics platform',
    status: 'Approved',
    createdBy: 'Maria Lopez',
    createdDate: '2024-06-20T13:00:00Z',
    lastModifiedBy: 'Maria Lopez',
    lastModifiedDate: '2024-06-20T13:00:00Z',
    projectedApr25: 9166.67,
    projectedMay25: 9166.67,
    projectedJun25: 9166.67,
    projectedJul25: 9166.67,
    projectedAug25: 9166.67,
    projectedSep25: 9166.67,
    projectedOct25: 9166.67,
    projectedNov25: 9166.67,
    projectedDec25: 9166.67,
    projectedJan26: 9166.67,
    projectedFeb26: 9166.67,
    projectedMar26: 9166.63,
    actualApr25: 0,
    actualMay25: 0,
    actualJun25: 0,
    actualJul25: 0,
    actualAug25: 0,
    actualSep25: 0,
    actualOct25: 0,
    actualNov25: 0,
    actualDec25: 0,
    actualJan26: 0,
    actualFeb26: 0,
    actualMar26: 0
  },
  {
    id: 'po-006',
    owner: 'John Doe',
    parentCompanyName: 'Retail Group',
    accountName: 'Retail Group India',
    domainVertical: 'Retail',
    region: 'North America',
    revenueType: 'Services',
    poNumber: 'PO-2025-006',
    poCurrency: 'USD',
    poValue: 80000,
    conversionRate: 83.5,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Negotiation',
    frequencyOfRealization: 'Quarterly',
    startDate: '2025-08-01',
    endDate: '2026-07-31',
    renewalUpsell: 'Renewal',
    productRevenueCategory: 'Professional Services',
    productName: 'HCL Retail Suite',
    projectDescription: 'Retail management services',
    status: 'Pending',
    createdBy: 'John Doe',
    createdDate: '2024-07-15T10:00:00Z',
    lastModifiedBy: 'John Doe',
    lastModifiedDate: '2024-07-15T10:00:00Z',
    projectedApr25: 20000,
    projectedMay25: 20000,
    projectedJun25: 20000,
    projectedJul25: 20000,
    projectedAug25: 20000,
    projectedSep25: 20000,
    projectedOct25: 20000,
    projectedNov25: 20000,
    projectedDec25: 20000,
    projectedJan26: 20000,
    projectedFeb26: 20000,
    projectedMar26: 20000,
    actualApr25: 0,
    actualMay25: 0,
    actualJun25: 0,
    actualJul25: 0,
    actualAug25: 0,
    actualSep25: 0,
    actualOct25: 0,
    actualNov25: 0,
    actualDec25: 0,
    actualJan26: 0,
    actualFeb26: 0,
    actualMar26: 0
  },
  {
    id: 'po-007',
    owner: 'Emily Chen',
    parentCompanyName: 'Logistics Corp.',
    accountName: 'Logistics Corp. APAC',
    domainVertical: 'Logistics',
    region: 'Asia',
    revenueType: 'License',
    poNumber: 'PO-2025-007',
    poCurrency: 'INR',
    poValue: 105000,
    conversionRate: 1,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Closed Won',
    frequencyOfRealization: 'Monthly',
    startDate: '2025-09-01',
    endDate: '2026-08-31',
    renewalUpsell: 'New',
    productRevenueCategory: 'Software',
    productName: 'HCL Logistics Suite',
    projectDescription: 'Logistics optimization software',
    status: 'Approved',
    createdBy: 'Emily Chen',
    createdDate: '2024-08-10T15:30:00Z',
    lastModifiedBy: 'Emily Chen',
    lastModifiedDate: '2024-08-10T15:30:00Z',
    projectedApr25: 8750,
    projectedMay25: 8750,
    projectedJun25: 8750,
    projectedJul25: 8750,
    projectedAug25: 8750,
    projectedSep25: 8750,
    projectedOct25: 8750,
    projectedNov25: 8750,
    projectedDec25: 8750,
    projectedJan26: 8750,
    projectedFeb26: 8750,
    projectedMar26: 8750,
    actualApr25: 0,
    actualMay25: 0,
    actualJun25: 0,
    actualJul25: 0,
    actualAug25: 0,
    actualSep25: 0,
    actualOct25: 0,
    actualNov25: 0,
    actualDec25: 0,
    actualJan26: 0,
    actualFeb26: 0,
    actualMar26: 0
  }
].map(po => calculateDerivedValues(po));

const ALL_COLUMNS = [
  { key: 'poNumber', label: 'PO Number', type: 'string' },
  { key: 'accountName', label: 'Account Name', type: 'string' },
  { key: 'productName', label: 'Product Name', type: 'string' },
  { key: 'poValue', label: 'PO Value', type: 'number' },
  { key: 'grandTotalINR', label: 'Grand Total (INR)', type: 'number' },
    { key: 'status', label: 'Status', type: 'string' },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ po, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
    <IconButton size="small" onClick={() => onView(po)} title="View Details"><Visibility fontSize="small" /></IconButton>
    <IconButton size="small" onClick={() => onEdit(po)} title="Edit"><Edit fontSize="small" /></IconButton>
    <IconButton size="small" color="error" onClick={() => onDelete([po.id])} title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

const PurchaseCard = ({ po, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(po.id)} onClick={e => e.stopPropagation()} />
    <AdminComponents.CardActionContainer>
      <ActionButtons po={po} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.PaddedCardContent>
      <Typography variant="h6" component="div" noWrap>{po.poNumber}</Typography>
      <Typography color="text.secondary" noWrap gutterBottom>{po.accountName}</Typography>
      <AdminComponents.StatusBadge ownerState={{ status: po.status }} label={po.status} size="small" />
      <AdminComponents.CardDivider />
      <AdminComponents.CardDetailsGrid>
        <AdminComponents.CardDetailLabel variant="body2">Product:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{po.productName}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">PO Value:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{po.poValue.toLocaleString('en-IN', { style: 'currency', currency: po.poCurrency })}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Grand Total (INR):</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{po.grandTotalINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}</Typography>
      </AdminComponents.CardDetailsGrid>
    </AdminComponents.PaddedCardContent>
  </AdminComponents.CardBase>
);

const PurchaseCompactCard = ({ po, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(po.id)} onClick={e => e.stopPropagation()} />
    <AdminComponents.CardActionContainer>
      <ActionButtons po={po} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.CompactCardContent>
      <div>
        <Typography variant="subtitle1" fontWeight="bold" noWrap>{po.poNumber}</Typography>
        <Typography variant="caption" color="text.secondary">{po.accountName}</Typography>
      </div>
      <AdminComponents.CompactCardFooter>
        <Typography variant="body2" fontWeight="500">{po.status}</Typography>
        <AdminComponents.StatusBadge ownerState={{ status: po.status }} label={po.status} size="small" />
      </AdminComponents.CompactCardFooter>
    </AdminComponents.CompactCardContent>
  </AdminComponents.CardBase>
);

const PurchaseListItem = ({ po, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.ListItemCard isSelected={isSelected}>
    <AdminComponents.ListItemGrid>
      <Checkbox checked={isChecked} onChange={() => onSelect(po.id)} onClick={e => e.stopPropagation()} />
      <Box>
        <Typography fontWeight="bold">{po.poNumber}</Typography>
        <Typography variant="body2" color="text.secondary">{po.accountName}</Typography>
      </Box>
      <Typography variant="body2">{po.productName}</Typography>
      <Typography variant="body2">{po.status}</Typography>
      <AdminComponents.StatusBadge ownerState={{ status: po.status }} label={po.status} size="small" />
      <AdminComponents.ListItemActions>
        <ActionButtons po={po} onView={onView} onEdit={onEdit} onDelete={onDelete} />
      </AdminComponents.ListItemActions>
    </AdminComponents.ListItemGrid>
  </AdminComponents.ListItemCard>
);

const PurchaseTable = ({ purchaseOrders, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, onDelete, onEdit, onView }) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
            <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < purchaseOrders.length} checked={purchaseOrders.length > 0 && selectedIds.length === purchaseOrders.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
          {purchaseOrders.map(po => (
            <TableRow key={po.id} hover selected={selectedId === po.id} onClick={() => onRowClick(po)}>
              <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(po.id)} onChange={() => onSelectOne(po.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => (
                <TableCell key={colKey}>
                  {colKey === 'poValue' || colKey === 'grandTotalINR'
                    ? po[colKey].toLocaleString('en-IN', { style: 'currency', currency: colKey === 'poValue' ? po.poCurrency : 'INR' })
                    : po[colKey]}
                </TableCell>
                            ))}
                            <TableCell align="center">
                <ActionButtons po={po} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

// TabPanel utility (from Customers.jsx)
const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div role="tabpanel" hidden={value !== index} id={`tabpanel-${index}`} aria-labelledby={`tab-${index}`} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const PURCHASE_STEPS = [
  'Company Info',
  'Financials',
  'Timeline',
  'Product',
  'Monthly/Actuals'
];

const PurchaseOrderDialog = ({ open, onClose, purchaseOrderData, mode, onSave }) => {
  const [formData, setFormData] = useState(emptyPurchaseOrder);
  const [tabValue, setTabValue] = useState(0);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    setFormData(purchaseOrderData || emptyPurchaseOrder);
    setTabValue(0);
  }, [purchaseOrderData, open]);

  const isViewOnly = mode === 'view';
  const title = mode === 'add' ? 'Add Purchase Order' : (mode === 'edit' ? 'Edit Purchase Order' : 'View Purchase Order');

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) setErrors(prev => ({ ...prev, [field]: undefined }));
  };

  const handleSave = () => {
    const validationErrors = validatePurchaseOrder(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    onSave(formData);
    onClose();
  };

  const handleTabChange = (event, newValue) => setTabValue(newValue);

  // --- Renderers for each step ---
  const renderCompanyInfo = () => (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={6} md={3}><TextField label="Owner *" name="owner" value={formData.owner} onChange={e => handleChange('owner', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" size="small" /></Grid>
      <Grid item xs={12} sm={6} md={3}><TextField label="Parent Company *" name="parentCompanyName" value={formData.parentCompanyName} onChange={e => handleChange('parentCompanyName', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" size="small" /></Grid>
      <Grid item xs={12} sm={6} md={3}><TextField label="Account Name *" name="accountName" value={formData.accountName} onChange={e => handleChange('accountName', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" size="small" /></Grid>
      <Grid item xs={12} sm={6} md={3}><TextField label="Domain/Vertical *" name="domainVertical" value={formData.domainVertical} onChange={e => handleChange('domainVertical', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" size="small" /></Grid>
      <Grid item xs={12} sm={6} md={4}>
        <FormControl fullWidth variant="outlined" disabled={isViewOnly} size="small" sx={{ minWidth: 180 }}>
          <InputLabel shrink>Region *</InputLabel>
          <Select label="Region *" value={formData.region} onChange={e => handleChange('region', e.target.value)} inputProps={{ 'aria-label': 'Region' }} MenuProps={{ PaperProps: { style: { fontSize: 16 } } }}>
            {dropdownOptions.region.map(opt => <MenuItem key={opt} value={opt} style={{ fontSize: 16 }}>{opt}</MenuItem>)}
          </Select>
        </FormControl>
      </Grid>
    </Grid>
  );

  const renderFinancials = () => (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={6} md={4}>
        <FormControl fullWidth variant="outlined" disabled={isViewOnly} size="small" sx={{ minWidth: 180 }}>
          <InputLabel shrink>Revenue Type *</InputLabel>
          <Select label="Revenue Type *" value={formData.revenueType} onChange={e => handleChange('revenueType', e.target.value)} inputProps={{ 'aria-label': 'Revenue Type' }} MenuProps={{ PaperProps: { style: { fontSize: 16 } } }}>
            {dropdownOptions.revenueType.map(opt => <MenuItem key={opt} value={opt} style={{ fontSize: 16 }}>{opt}</MenuItem>)}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={4}><TextField label="PO Number *" name="poNumber" value={formData.poNumber} onChange={e => handleChange('poNumber', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" size="small" /></Grid>
      <Grid item xs={12} sm={6} md={4}>
        <FormControl fullWidth variant="outlined" disabled={isViewOnly} size="small" sx={{ minWidth: 180 }}>
          <InputLabel shrink>Currency *</InputLabel>
          <Select label="Currency *" value={formData.poCurrency} onChange={e => handleChange('poCurrency', e.target.value)} inputProps={{ 'aria-label': 'Currency' }} MenuProps={{ PaperProps: { style: { fontSize: 16 } } }}>
            {dropdownOptions.poCurrency.map(opt => <MenuItem key={opt} value={opt} style={{ fontSize: 16 }}>{opt}</MenuItem>)}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={4}><TextField label="PO Value *" name="poValue" type="number" value={formData.poValue} onChange={e => handleChange('poValue', parseFloat(e.target.value) || 0)} fullWidth disabled={isViewOnly} variant="outlined" size="small" /></Grid>
      <Grid item xs={12} sm={6} md={4}><TextField label="Conversion Rate *" name="conversionRate" type="number" value={formData.conversionRate} onChange={e => handleChange('conversionRate', parseFloat(e.target.value) || 0)} fullWidth disabled={isViewOnly} variant="outlined" size="small" /></Grid>
      <Grid item xs={12} sm={6} md={4}><TextField label="HCL USD Rate *" name="hclConversionRateUSD" type="number" value={formData.hclConversionRateUSD} onChange={e => handleChange('hclConversionRateUSD', parseFloat(e.target.value) || 0)} fullWidth disabled={isViewOnly} variant="outlined" size="small" /></Grid>
    </Grid>
  );

  const renderTimeline = () => (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={6} md={4}>
        <FormControl fullWidth variant="outlined" disabled={isViewOnly} size="small" sx={{ minWidth: 180 }}>
          <InputLabel shrink>Opportunity Stage *</InputLabel>
          <Select label="Opportunity Stage *" value={formData.opportunityStage} onChange={e => handleChange('opportunityStage', e.target.value)} inputProps={{ 'aria-label': 'Opportunity Stage' }} MenuProps={{ PaperProps: { style: { fontSize: 16 } } }}>
            {dropdownOptions.opportunityStage.map(opt => <MenuItem key={opt} value={opt} style={{ fontSize: 16 }}>{opt}</MenuItem>)}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <FormControl fullWidth variant="outlined" disabled={isViewOnly} size="small" sx={{ minWidth: 180 }}>
          <InputLabel shrink>Frequency *</InputLabel>
          <Select label="Frequency *" value={formData.frequencyOfRealization} onChange={e => handleChange('frequencyOfRealization', e.target.value)} inputProps={{ 'aria-label': 'Frequency' }} MenuProps={{ PaperProps: { style: { fontSize: 16 } } }}>
            {dropdownOptions.frequencyOfRealization.map(opt => <MenuItem key={opt} value={opt} style={{ fontSize: 16 }}>{opt}</MenuItem>)}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={4}><TextField label="Start Date *" name="startDate" type="date" value={formData.startDate} onChange={e => handleChange('startDate', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" size="small" InputLabelProps={{ shrink: true }} /></Grid>
      <Grid item xs={12} sm={6} md={4}><TextField label="End Date *" name="endDate" type="date" value={formData.endDate} onChange={e => handleChange('endDate', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" size="small" InputLabelProps={{ shrink: true }} /></Grid>
    </Grid>
  );

  const renderProduct = () => (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={6} md={4}>
        <FormControl fullWidth variant="outlined" disabled={isViewOnly} size="small" sx={{ minWidth: 180 }}>
          <InputLabel shrink>Renewal/Upsell *</InputLabel>
          <Select label="Renewal/Upsell *" value={formData.renewalUpsell} onChange={e => handleChange('renewalUpsell', e.target.value)} inputProps={{ 'aria-label': 'Renewal/Upsell' }} MenuProps={{ PaperProps: { style: { fontSize: 16 } } }}>
            {dropdownOptions.renewalUpsell.map(opt => <MenuItem key={opt} value={opt} style={{ fontSize: 16 }}>{opt}</MenuItem>)}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <FormControl fullWidth variant="outlined" disabled={isViewOnly} size="small" sx={{ minWidth: 180 }}>
          <InputLabel shrink>Product Category *</InputLabel>
          <Select label="Product Category *" value={formData.productRevenueCategory} onChange={e => handleChange('productRevenueCategory', e.target.value)} inputProps={{ 'aria-label': 'Product Category' }} MenuProps={{ PaperProps: { style: { fontSize: 16 } } }}>
            {dropdownOptions.productRevenueCategory.map(opt => <MenuItem key={opt} value={opt} style={{ fontSize: 16 }}>{opt}</MenuItem>)}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={4}><TextField label="Product Name *" name="productName" value={formData.productName} onChange={e => handleChange('productName', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" size="small" /></Grid>
      <Grid item xs={12} sm={6} md={4}><TextField label="Project Description" name="projectDescription" value={formData.projectDescription} onChange={e => handleChange('projectDescription', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" size="small" /></Grid>
    </Grid>
  );

  const renderMonthlyActuals = () => (
    <Grid container spacing={2} alignItems="center">
      {['Apr25','May25','Jun25','Jul25','Aug25','Sep25','Oct25','Nov25','Dec25','Jan26','Feb26','Mar26'].map(month => (
        <Grid item xs={12} sm={6} md={3} key={month}>
          <TextField label={`Projected ${month}`} name={`projected${month}`} type="number" value={formData[`projected${month}`]} onChange={e => handleChange(`projected${month}`, parseFloat(e.target.value) || 0)} fullWidth disabled={isViewOnly} variant="outlined" size="small" />
          <TextField label={`Actual ${month}`} name={`actual${month}`} type="number" value={formData[`actual${month}`]} onChange={e => handleChange(`actual${month}`, parseFloat(e.target.value) || 0)} fullWidth disabled={isViewOnly} variant="outlined" size="small" sx={{ mt: 1 }} />
        </Grid>
      ))}
    </Grid>
  );

    return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <AdminComponents.DialogHeader>
        <AdminComponents.DialogProgressContainer>
          <Typography variant="h6">{title}</Typography>
        </AdminComponents.DialogProgressContainer>
        <Box>
          {!isViewOnly && <Button color="inherit" startIcon={<Save />} onClick={handleSave}>Save</Button>}
          <Button color="inherit" startIcon={<Close />} onClick={onClose}>Exit</Button>
        </Box>
      </AdminComponents.DialogHeader>
      {/* Removed extra row of step/tab titles above the stepper/tabs */}
      <Box sx={{ backgroundColor: 'var(--background-secondary)', pt: 2 }}>
        <Box sx={{ px: 3 }}>
          <Stepper alternativeLabel activeStep={tabValue} connector={<AdminComponents.CustomStepConnector />}>
            {PURCHASE_STEPS.map((label, index) => (
              <Step key={label} completed={tabValue > index}>
                <StepLabel />
              </Step>
            ))}
          </Stepper>
        </Box>
        <Box>
          <AdminComponents.StyledTabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            aria-label="purchase order details tabs"
          >
            {PURCHASE_STEPS.map((label) => (
              <AdminComponents.StyledTab key={label} label={label} />
            ))}
          </AdminComponents.StyledTabs>
        </Box>
      </Box>
      <DialogContent sx={{ p: 0, overflow: 'unset' }}>
        <AdminComponents.FixedHeightDialogContent>
          <TabPanel value={tabValue} index={0}>{renderCompanyInfo()}</TabPanel>
          <TabPanel value={tabValue} index={1}>{renderFinancials()}</TabPanel>
          <TabPanel value={tabValue} index={2}>{renderTimeline()}</TabPanel>
          <TabPanel value={tabValue} index={3}>{renderProduct()}</TabPanel>
          <TabPanel value={tabValue} index={4}>{renderMonthlyActuals()}</TabPanel>
        </AdminComponents.FixedHeightDialogContent>
      </DialogContent>
      <AdminComponents.DialogFooter>
        <AdminComponents.DialogSummary>
          <Typography variant="body2"><strong>Status:</strong> {formData.status}</Typography>
          <Typography variant="body2"><strong>PO Number:</strong> {formData.poNumber}</Typography>
          <Typography variant="body2"><strong>Currency:</strong> {formData.poCurrency}</Typography>
        </AdminComponents.DialogSummary>
        <Box>
          {!isViewOnly && <Button variant="contained" startIcon={<Save />} onClick={handleSave}>Save</Button>}
          <Button variant="outlined" startIcon={<Close />} onClick={onClose}>Exit</Button>
        </Box>
      </AdminComponents.DialogFooter>
    </Dialog>
  );
};

const METRIC_OPTIONS = [
  { value: 'revenue', label: 'Revenue (Projected/Actual)' },
  { value: 'status', label: 'Status Distribution' },
  { value: 'region', label: 'Region Distribution' }
];

// Add PurchaseOrderGraph component
const PurchaseOrderGraph = ({ purchaseOrders, chartType, metric, selectedPO }) => {
  const chartRef = React.useRef(null);
  const chartInstance = React.useRef(null);

  // Calculate data for the selected metric
  const summaryStats = React.useMemo(() => {
    const statusCounts = purchaseOrders.reduce((acc, po) => {
      acc[po.status] = (acc[po.status] || 0) + 1;
      return acc;
    }, {});
    const regionCounts = purchaseOrders.reduce((acc, po) => {
      acc[po.region] = (acc[po.region] || 0) + 1;
      return acc;
    }, {});
    const months = ['Apr25', 'May25', 'Jun25', 'Jul25', 'Aug25', 'Sep25', 'Oct25', 'Nov25', 'Dec25', 'Jan26', 'Feb26', 'Mar26'];
    const monthlyProjected = months.map(month => purchaseOrders.reduce((sum, po) => sum + (po[`projected${month}`] || 0), 0));
    const monthlyActual = months.map(month => purchaseOrders.reduce((sum, po) => sum + (po[`actual${month}`] || 0), 0));
    return { statusCounts, regionCounts, monthlyProjected, monthlyActual };
  }, [purchaseOrders]);

  // Prepare chart data
  const getChartData = () => {
    const chartBackgrounds = theme.palette.chart.backgrounds;
    const barLineColor = theme.palette.primary.main;
    const borderColor = theme.palette.primary.dark;
    if (metric === 'region') {
      return {
        labels: Object.keys(summaryStats.regionCounts),
        datasets: [{
          label: 'Purchase Orders by Region',
          data: Object.values(summaryStats.regionCounts),
          backgroundColor: ['pie', 'doughnut'].includes(chartType) ? chartBackgrounds : barLineColor,
          borderColor: borderColor,
        }]
      };
    }
    if (metric === 'status') {
      return {
        labels: Object.keys(summaryStats.statusCounts),
        datasets: [{
          label: 'Purchase Orders by Status',
          data: Object.values(summaryStats.statusCounts),
          backgroundColor: ['pie', 'doughnut'].includes(chartType) ? chartBackgrounds : barLineColor,
          borderColor: borderColor,
        }]
      };
    }
    if (metric === 'revenue') {
      const months = ["Apr'25", "May'25", "Jun'25", "Jul'25", "Aug'25", "Sep'25", "Oct'25", "Nov'25", "Dec'25", "Jan'26", "Feb'26", "Mar'26"];
      return {
        labels: months,
        datasets: [
          {
            label: 'Projected Revenue (INR)',
            data: summaryStats.monthlyProjected,
            backgroundColor: chartType === 'line' ? barLineColor : barLineColor,
            borderColor: borderColor,
            borderWidth: 2,
            fill: chartType !== 'line',
          },
          {
            label: 'Actual Revenue (INR)',
            data: summaryStats.monthlyActual,
            backgroundColor: chartType === 'line' ? chartBackgrounds[1] : chartBackgrounds[1],
            borderColor: borderColor,
            borderWidth: 2,
            fill: chartType !== 'line',
          }
        ]
      };
    }
    return { labels: [], datasets: [] };
  };

  React.useEffect(() => {
    if (chartInstance.current) chartInstance.current.destroy();
    if (chartRef.current && purchaseOrders.length > 0) {
      const ctx = chartRef.current.getContext('2d');
      const data = getChartData();
      chartInstance.current = new Chart(ctx, {
        type: chartType === 'doughnut' ? 'doughnut' : chartType,
        data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: metric === 'revenue'
                ? 'Purchase Orders - Revenue (Projected/Actual)'
                : metric === 'region'
                  ? 'Purchase Orders - Region Distribution'
                  : 'Purchase Orders - Status Distribution'
            },
            legend: { position: 'top' }
          },
          scales: ['pie', 'doughnut'].includes(chartType) ? {} : {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return new Intl.NumberFormat('en-IN', {
                    style: 'currency',
                    currency: 'INR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                  }).format(value);
                }
              }
            }
          }
        }
      });
    }
    return () => { if (chartInstance.current) chartInstance.current.destroy(); };
  }, [purchaseOrders, chartType, metric]);

  return (
    <AdminComponents.GraphCanvasContainer>
      <canvas ref={chartRef}></canvas>
    </AdminComponents.GraphCanvasContainer>
  );
};

const Purchase = () => {
  const [purchaseOrders, setPurchaseOrders] = useState(initialPurchaseOrders);
  const [selectedPO, setSelectedPO] = useState(null);
  const [sortColumn, setSortColumn] = useState('poNumber');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [viewMode, setViewMode] = useState('cards');
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const [groupByKeys, setGroupByKeys] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState('add');
  const [dialogPO, setDialogPO] = useState(null);
  const [selectedMetric, setSelectedMetric] = useState('region');
  const [chartType, setChartType] = useState('doughnut');
  const [isGraphVisible, setIsGraphVisible] = useState(false);

  const handleOpenDialog = (mode, po = null) => {
    setDialogMode(mode);
    setDialogPO(po);
    setDialogOpen(true);
  };
  const handleCloseDialog = () => setDialogOpen(false);
  const handleSaveDialog = (po) => {
    if (dialogMode === 'add') {
      setPurchaseOrders(prev => [{ ...po, id: `po-${Date.now()}` }, ...prev]);
    } else if (dialogMode === 'edit') {
      setPurchaseOrders(prev => prev.map(p => p.id === po.id ? po : p));
    }
  };

  // --- Summary Stats ---
  const summaryStats = useMemo(() => {
    const total = purchaseOrders.length;
    const totalValue = purchaseOrders.reduce((sum, po) => sum + (po.poValue || 0), 0);
    const avgValue = total > 0 ? totalValue / total : 0;
    return { total, totalValue, avgValue };
  }, [purchaseOrders]);

  // --- Filtering, Searching, Sorting ---
  const processedPurchaseOrders = useMemo(() => {
    let current = purchaseOrders;
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
      current = current.filter(po =>
        po.poNumber.toLowerCase().includes(term) ||
        po.accountName.toLowerCase().includes(term) ||
        po.productName.toLowerCase().includes(term)
      );
        }
        // Advanced filters
        if (activeFilters.length > 0) {
      current = current.filter(po => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
          const poValue = String(po[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();
                    switch (operator) {
            case 'Equals': return poValue === filterValue;
            case 'Not Equals': return poValue !== filterValue;
            case 'Contains': return poValue.includes(filterValue);
            case 'Starts With': return poValue.startsWith(filterValue);
            case 'Ends With': return poValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }
        // Sorting
        return [...current].sort((a, b) => {
            const valA = a[sortColumn], valB = b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? valA - valB : valB - valA;
        });
  }, [purchaseOrders, searchTerm, sortColumn, sortDirection, activeFilters]);

  const handleDeleteRequest = (ids) => {};
  const handleShowDetails = (po) => {};
  const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedPurchaseOrders.map(po => po.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);

  const displayPO = useMemo(() => {
    const isSelectedVisible = processedPurchaseOrders.some(po => po.id === selectedPO?.id);
    if (isSelectedVisible) return selectedPO;
    return processedPurchaseOrders.length > 0 ? processedPurchaseOrders[0] : null;
  }, [processedPurchaseOrders, selectedPO]);

    const renderCurrentView = () => (
        <AdminComponents.ViewContainer>
      {processedPurchaseOrders.length > 0 ? (
                <>
          {viewMode === 'cards' && <AdminComponents.GridView>{processedPurchaseOrders.map(po => <PurchaseCard key={po.id} po={po} isSelected={displayPO?.id === po.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(po.id)} onDelete={handleDeleteRequest} onEdit={po => handleOpenDialog('edit', po)} onView={po => handleOpenDialog('view', po)} />)}</AdminComponents.GridView>}
                    {viewMode === 'grid' && (
                        <PurchaseTable
              purchaseOrders={processedPurchaseOrders}
              onRowClick={setSelectedPO}
                            onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
              selectedId={displayPO?.id}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDeleteRequest}
              onEdit={po => handleOpenDialog('edit', po)}
              onView={po => handleOpenDialog('view', po)}
                        />
                    )}
          {viewMode === 'compact' && <AdminComponents.CompactView>{processedPurchaseOrders.map(po => <PurchaseCompactCard key={po.id} po={po} isSelected={displayPO?.id === po.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(po.id)} onDelete={handleDeleteRequest} onEdit={po => handleOpenDialog('edit', po)} onView={po => handleOpenDialog('view', po)} />)}</AdminComponents.CompactView>}
          {viewMode === 'list' && <AdminComponents.ListView>{processedPurchaseOrders.map(po => <PurchaseListItem key={po.id} po={po} isSelected={displayPO?.id === po.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(po.id)} onDelete={handleDeleteRequest} onEdit={po => handleOpenDialog('edit', po)} onView={po => handleOpenDialog('view', po)} />)}</AdminComponents.ListView>}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Matching Purchase Orders</Typography>
                    <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

  // Sidebar, advanced search, and table settings (structure matches Customers.jsx)
  // ... (omitted for brevity, but will match Customers.jsx patterns)

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                  <AdminComponents.SummaryCard isActive={true}>
                                        <AdminComponents.SummaryAvatar variant="total"><People /></AdminComponents.SummaryAvatar>
                    <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total Purchase Orders</Typography></Box>
                                    </AdminComponents.SummaryCard>
                  <AdminComponents.SummaryCard isActive={true}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                    <Box><Typography variant="h6">{summaryStats.totalValue.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}</Typography><Typography variant="body2">Total Value</Typography></Box>
                                    </AdminComponents.SummaryCard>
                  <AdminComponents.SummaryCard isActive={true}>
                    <AdminComponents.SummaryAvatar variant="inactive"><Cancel /></AdminComponents.SummaryAvatar>
                    <Box><Typography variant="h6">{summaryStats.avgValue.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}</Typography><Typography variant="body2">Average Value</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                  <Button variant="contained" startIcon={<Add />} onClick={() => handleOpenDialog('add')}>Add Purchase Order</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={() => setIsGraphVisible(v => !v)}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>
                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedPurchaseOrders.length > 0 && selectedIds.length === processedPurchaseOrders.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedPurchaseOrders.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => setIsSidebarOpen(true) || setSidebarMode('search')}>Advanced Search</Button>
                <Button variant="outlined" startIcon={<Settings />} onClick={() => setIsSidebarOpen(true) || setSidebarMode('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>
                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
      <AdminComponents.ChartTypeSelectorContainer>
        <AdminComponents.StyledToggleButtonGroup
          value={chartType}
          exclusive
          onChange={(e, newType) => newType && setChartType(newType)}
          size="small"
          fullWidth
        >
          <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
          <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
          <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
          <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
        </AdminComponents.StyledToggleButtonGroup>
      </AdminComponents.ChartTypeSelectorContainer>
      <FormControl size="small" sx={{ minWidth: 200, mt: 2 }}>
        <InputLabel shrink>Metric</InputLabel>
        <Select
          value={selectedMetric}
          label="Metric"
          onChange={e => setSelectedMetric(e.target.value)}
          inputProps={{ 'aria-label': 'Metric' }}
          MenuProps={{ PaperProps: { style: { fontSize: 16 } } }}
        >
          {METRIC_OPTIONS.map(opt => (
            <MenuItem key={opt.value} value={opt.value} style={{ fontSize: 16 }}>{opt.label}</MenuItem>
          ))}
        </Select>
      </FormControl>
      <PurchaseOrderGraph purchaseOrders={purchaseOrders} chartType={chartType} metric={selectedMetric} selectedPO={displayPO} />
    </AdminComponents.DetailsPane>
  </AdminComponents.ContentBody>
</AdminComponents.MainContentArea>

  {/* Restore Drawer for Advanced Search & Table Settings */}
  <Drawer
    variant="persistent"
    anchor="right"
    open={isSidebarOpen}
  >
    <AdminComponents.SidebarContainer>
      <AdminComponents.SidebarHeader>
        <Typography variant="h6">
          {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
        </Typography>
        <IconButton onClick={() => setIsSidebarOpen(false)}>
          <Cancel />
        </IconButton>
      </AdminComponents.SidebarHeader>
      <AdminComponents.SidebarContent>
        {sidebarMode === 'search' && (
          <>
            <AdminComponents.SidebarSection>
              <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
              <FormControl fullWidth size="small">
                <InputLabel>Field</InputLabel>
                <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                  {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                </Select>
              </FormControl>
              <FormControl fullWidth size="small">
                <InputLabel>Operator</InputLabel>
                <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                  {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                </Select>
              </FormControl>
              <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))}/>
              <Button variant="outlined" fullWidth onClick={() => {
                if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
                  setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
                  setFilterBuilder({ field: '', operator: '', value: '' });
                }
              }}>Add Filter</Button>
            </AdminComponents.SidebarSection>
            <AdminComponents.SidebarSection>
              <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
              <AdminComponents.FilterChipContainer>
                {stagedFilters.length > 0 ? stagedFilters.map(f => (
                  <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
              </AdminComponents.FilterChipContainer>
            </AdminComponents.SidebarSection>
            <AdminComponents.SidebarSection>
              <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
              <AdminComponents.FilterChipContainer>
                {activeFilters.length > 0 ? activeFilters.map(f => (
                  <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
              </AdminComponents.FilterChipContainer>
            </AdminComponents.SidebarSection>
          </>
        )}
        {sidebarMode === 'grid' && (
          <>
            <AdminComponents.SidebarSection>
              <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
              <AdminComponents.ColumnActionContainer>
                <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
              </AdminComponents.ColumnActionContainer>
              <AdminComponents.ColumnVisibilityContainer>
                {ALL_COLUMNS.map(col => (
                  <FormControlLabel
                    key={col.key}
                    control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => {
                      const isVisible = columnOrder.includes(col.key);
                      let newOrder;
                      if (isVisible) {
                        if (columnOrder.length > 1) {
                          newOrder = columnOrder.filter(key => key !== col.key);
                        } else {
                          return;
                        }
                      } else {
                        const originalKeys = ALL_COLUMNS.map(c => c.key);
                        newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === col.key);
                      }
                      setColumnOrder(newOrder);
                    }} name={col.key} />}
                    label={col.label}
                  />
                ))}
              </AdminComponents.ColumnVisibilityContainer>
            </AdminComponents.SidebarSection>
            <AdminComponents.SidebarSection>
              <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
              <AdminComponents.FilterChipContainer>
                {groupByKeys.length > 0 ? groupByKeys.map(key => (
                  <Chip
                    key={key}
                    label={ALL_COLUMNS.find(c => c.key === key)?.label}
                    onDelete={() => setGroupByKeys(groupByKeys.filter(k => k !== key))}
                  />
                )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
              </AdminComponents.FilterChipContainer>
              <AdminComponents.ColumnVisibilityContainer>
                {ALL_COLUMNS.map(col => (
                  <FormControlLabel
                    key={col.key}
                    control={
                      <Checkbox
                        checked={groupByKeys.includes(col.key)}
                        onChange={() => setGroupByKeys(prev => prev.includes(col.key) ? prev.filter(k => k !== col.key) : [...prev, col.key])}
                      />
                    }
                    label={col.label}
                  />
                ))}
              </AdminComponents.ColumnVisibilityContainer>
            </AdminComponents.SidebarSection>
          </>
        )}
      </AdminComponents.SidebarContent>
      <AdminComponents.SidebarFooter>
        {sidebarMode === 'search' && (
          <>
            <Button variant="outlined" onClick={() => { setStagedFilters([]); setActiveFilters([]); }}>Reset</Button>
            <Button variant="contained" color="primary" onClick={() => { setActiveFilters([...activeFilters, ...stagedFilters]); setStagedFilters([]); }}>Apply</Button>
          </>
        )}
        {sidebarMode === 'grid' && (
          <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
        )}
      </AdminComponents.SidebarFooter>
    </AdminComponents.SidebarContainer>
  </Drawer>

  {/* Restore PurchaseOrderDialog for Add/Edit/View */}
  <PurchaseOrderDialog
    open={dialogOpen}
    onClose={handleCloseDialog}
    purchaseOrderData={dialogPO}
    mode={dialogMode}
    onSave={handleSaveDialog}
  />
</AdminComponents.AppBody>
</AdminComponents.AppContainer>
</ThemeProvider>
    );
};

export default Purchase; 